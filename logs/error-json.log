{"instant":{"epochSecond":1736929015,"nanoOfSecond":797260400},"thread":"main","level":"ERROR","loggerName":"com.qihoo.feature.FeatureTransApplication","message":"test roll error log","endOfBatch":false,"loggerFqcn":"org.apache.logging.slf4j.Log4jLogger","source":{"classLoaderName":"app","class":"com.qihoo.feature.FeatureTransApplication","method":"main","file":"FeatureTransApplication.java","line":14},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1737454158,"nanoOfSecond":87670900},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1737454205,"nanoOfSecond":320654900},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1741598540,"nanoOfSecond":22433500},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1744177262,"nanoOfSecond":543856600},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1744177326,"nanoOfSecond":4424400},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1744178631,"nanoOfSecond":691197800},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1744178747,"nanoOfSecond":91908400},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1747986459,"nanoOfSecond":913062500},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
{"instant":{"epochSecond":1750931939,"nanoOfSecond":623854900},"thread":"Test worker","level":"ERROR","loggerName":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","message":"Communications link failure\n\nThe last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.","endOfBatch":false,"loggerFqcn":"org.jboss.logging.DelegatingBasicLogger","source":{"classLoaderName":"app","class":"org.hibernate.engine.jdbc.spi.SqlExceptionHelper","method":"logExceptions","file":"SqlExceptionHelper.java","line":150},"threadId":1,"threadPriority":5}
