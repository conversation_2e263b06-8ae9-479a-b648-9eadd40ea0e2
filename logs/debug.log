2025-07-01 18:12:52,242 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Neither @ContextConfiguration nor @ContextHierarchy found for test class [DagParseTest]: using SpringBootContextLoader
2025-07-01 18:12:52,287 DEBUG org.springframework.test.context.support.AbstractContextLoader [Test worker] Could not detect default resource locations for test class [com.qihoo.feature.DagParseTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-07-01 18:12:52,300 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Using ContextCustomizerFactory implementations for test class [DagParseTest]: [OnFailureConditionReportContextCustomizerFactory, OverrideAutoConfigurationContextCustomizerFactory, ObservabilityContextCustomizerFactory, TypeExcludeFiltersContextCustomizerFactory, PropertyMappingContextCustomizerFactory, WebDriverContextCustomizerFactory, ImportsContextCustomizerFactory, ExcludeFilterContextCustomizerFactory, HttpGraphQlTesterContextCustomizerFactory, DuplicateJsonObjectContextCustomizerFactory, MockitoContextCustomizerFactory, TestRestTemplateContextCustomizerFactory, WebTestClientContextCustomizerFactory, DisableReactorResourceFactoryGlobalResourcesContextCustomizerFactory, BeanOverrideContextCustomizerFactory, MockServerContainerContextCustomizerFactory, DynamicPropertiesContextCustomizerFactory]
2025-07-01 18:12:52,334 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Using ContextCustomizers for test class [DagParseTest]: [OnFailureConditionReportContextCustomizer, DisableObservabilityContextCustomizer, PropertyMappingContextCustomizer, WebDriverContextCustomizer, ExcludeFilterContextCustomizer, DuplicateJsonObjectContextCustomizer, MockitoContextCustomizer, TestRestTemplateContextCustomizer, DisableReactorResourceFactoryGlobalResourcesContextCustomizerCustomizer, DynamicPropertiesContextCustomizer]
2025-07-01 18:12:52,474 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\FeatureTransApplication.class]
2025-07-01 18:12:52,651 DEBUG org.springframework.test.context.util.TestContextFailureHandler [Test worker] Skipping candidate TestExecutionListener [org.springframework.test.context.observation.MicrometerObservationRegistryTestExecutionListener] due to a missing dependency. Specify custom TestExecutionListener classes or make the default TestExecutionListener classes and their required dependencies available. Offending class: io.micrometer.context.ThreadLocalAccessor. MicrometerObservationRegistryTestExecutionListener requires io.micrometer:micrometer-observation:1.10.8 or higher and io.micrometer:context-propagation:1.0.3 or higher.
2025-07-01 18:12:52,679 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Using TestExecutionListeners for test class [DagParseTest]: [ServletTestExecutionListener, DirtiesContextBeforeModesTestExecutionListener, ApplicationEventsTestExecutionListener, MockitoTestExecutionListener, BeanOverrideTestExecutionListener, DependencyInjectionTestExecutionListener, DirtiesContextTestExecutionListener, CommonCachesTestExecutionListener, TransactionalTestExecutionListener, SqlScriptsTestExecutionListener, EventPublishingTestExecutionListener, RestDocsTestExecutionListener, MockRestServiceServerResetTestExecutionListener, MockMvcPrintOnlyOnFailureTestExecutionListener, WebDriverTestExecutionListener, MockWebServiceServerTestExecutionListener, ResetMocksTestExecutionListener, MockitoResetTestExecutionListener]
2025-07-01 18:12:52,683 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] Before test class: class [DagParseTest], class annotated with @DirtiesContext [false] with mode [null]
2025-07-01 18:12:53,244 DEBUG org.springframework.boot.StartupInfoLogger [Test worker] Running with Spring Boot v3.4.1, Spring v6.2.1
2025-07-01 18:12:53,248 DEBUG org.springframework.boot.SpringApplication [Test worker] Loading source class com.qihoo.feature.FeatureTransApplication
2025-07-01 18:12:53,353 DEBUG org.springframework.context.support.AbstractApplicationContext [Test worker] Refreshing org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c
2025-07-01 18:12:53,391 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-07-01 18:12:53,428 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-07-01 18:12:53,503 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\DataSourceConfig.class]
2025-07-01 18:12:53,508 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\DateColumnMapRowMapper.class]
2025-07-01 18:12:53,518 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\FepDataSourceConfig.class]
2025-07-01 18:12:53,520 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\JacksonConfig.class]
2025-07-01 18:12:53,522 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\LocalDataSourceConfig.class]
2025-07-01 18:12:53,574 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Ignored because not a concrete top-level class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\local\FeatureProcessDefinitionRepository.class]
2025-07-01 18:12:53,577 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\service\X6DataParser.class]
2025-07-01 18:12:53,579 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\service\X6DataParserbackup.class]
2025-07-01 18:12:54,444 DEBUG org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Scanning for JPA repositories in packages com.qihoo.feature.repository.fep.
2025-07-01 18:12:54,456 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\fep] does not exist
2025-07-01 18:12:54,463 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureCategoryConfigRepository.class]
2025-07-01 18:12:54,464 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureCategoryInfoRepository.class]
2025-07-01 18:12:54,467 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureInfoRepository.class]
2025-07-01 18:12:54,468 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureProcessRepository.class]
2025-07-01 18:12:54,521 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*Impl.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\fep] does not exist
2025-07-01 18:12:54,567 DEBUG org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Scanning for JPA repositories in packages com.qihoo.feature.repository.local.
2025-07-01 18:12:54,569 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\local] does not exist
2025-07-01 18:12:54,573 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\local\FeatureProcessDefinitionRepository.class]
2025-07-01 18:12:54,580 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*Impl.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\local] does not exist
2025-07-01 18:12:55,530 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-07-01 18:12:55,534 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.test.mock.mockito.MockitoPostProcessor'
2025-07-01 18:12:55,553 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'emBeanDefinitionRegistrarPostProcessor'
2025-07-01 18:12:55,554 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor'
2025-07-01 18:12:55,634 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-07-01 18:12:55,637 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-07-01 18:12:55,638 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-07-01 18:12:55,638 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-07-01 18:12:55,647 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor'
2025-07-01 18:12:55,651 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-07-01 18:12:55,655 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-07-01 18:12:55,662 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalPersistenceAnnotationProcessor'
2025-07-01 18:12:55,663 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-07-01 18:12:55,664 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-07-01 18:12:55,670 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-07-01 18:12:55,708 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-07-01 18:12:55,713 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-07-01 18:12:55,720 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-07-01 18:12:55,721 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-07-01 18:12:55,723 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'projectingArgumentResolverBeanPostProcessor'
2025-07-01 18:12:55,726 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-07-01 18:12:55,726 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-07-01 18:12:55,768 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionAttributeSource'
2025-07-01 18:12:55,771 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionInterceptor'
2025-07-01 18:12:55,771 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-07-01 18:12:55,794 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-07-01 18:12:55,795 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-07-01 18:12:55,850 DEBUG org.springframework.ui.context.support.UiApplicationContextUtils [Test worker] Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@1b0e031b]
2025-07-01 18:12:55,850 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.test.context.support.internalDynamicPropertyRegistrarBeanInitializer'
2025-07-01 18:12:55,858 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dataSourceScriptDatabaseInitializer'
2025-07-01 18:12:55,858 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration'
2025-07-01 18:12:55,866 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localDataSource'
2025-07-01 18:12:55,867 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dataSourceConfig'
2025-07-01 18:12:55,912 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
2025-07-01 18:12:55,995 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
2025-07-01 18:12:56,002 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'localDataSource'
2025-07-01 18:12:56,003 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
2025-07-01 18:12:56,016 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFactoryFep'
2025-07-01 18:12:56,017 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'fepDataSourceConfig'
2025-07-01 18:12:56,021 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'fepDataSource'
2025-07-01 18:12:56,034 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties'
2025-07-01 18:12:56,054 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties'
2025-07-01 18:12:56,058 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFactoryBuilder'
2025-07-01 18:12:56,059 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration'
2025-07-01 18:12:56,064 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'localDataSource'
2025-07-01 18:12:56,065 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties'
2025-07-01 18:12:56,066 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-07-01 18:12:56,066 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties'
2025-07-01 18:12:56,070 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
2025-07-01 18:12:56,071 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
2025-07-01 18:12:56,084 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaVendorAdapter'
2025-07-01 18:12:56,122 DEBUG org.jboss.logging.LoggerProviders [Test worker] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-07-01 18:12:56,142 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFactoryBuilder' via factory method to bean named 'jpaVendorAdapter'
2025-07-01 18:12:56,153 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFactoryFep' via factory method to bean named 'entityManagerFactoryBuilder'
2025-07-01 18:12:56,181 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\entity\fep] does not exist
2025-07-01 18:12:56,185 DEBUG org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean [Test worker] Building JPA container EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-07-01 18:13:05,552 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFactoryLocal'
2025-07-01 18:13:05,553 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localDataSourceConfig'
2025-07-01 18:13:05,557 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFactoryLocal' via factory method to bean named 'entityManagerFactoryBuilder'
2025-07-01 18:13:05,560 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\entity\local] does not exist
2025-07-01 18:13:05,560 DEBUG org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean [Test worker] Building JPA container EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-07-01 18:13:06,289 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureTransApplication'
2025-07-01 18:13:06,292 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dateColumnMapRowMapper'
2025-07-01 18:13:06,294 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jacksonConfig'
2025-07-01 18:13:06,297 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'x6DataParser'
2025-07-01 18:13:06,306 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureProcessRepository'
2025-07-01 18:13:06,320 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#0'
2025-07-01 18:13:06,328 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureProcessRepository.fragments#0'
2025-07-01 18:13:06,338 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaSharedEM_entityManagerFactoryFep'
2025-07-01 18:13:06,387 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaMappingContext'
2025-07-01 18:13:06,388 DEBUG org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean [Test worker] Initializing JpaMetamodelMappingContext…
2025-07-01 18:13:06,422 DEBUG org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean [Test worker] Finished initializing JpaMetamodelMappingContext
2025-07-01 18:13:06,471 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,539 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,644 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureProcessRepository…
2025-07-01 18:13:06,671 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,756 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureProcessEntity.findByFeatureCategoryKeyAndStatus'
2025-07-01 18:13:06,763 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureProcessEntity.findByFeatureCategoryKeyAndStatus
2025-07-01 18:13:06,771 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,906 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureProcessEntity.findFeatureProcessEntitiesByFeatureCategoryKeyInAndStatusOrderByFeatureCategoryKeyAscRunOrderAsc'
2025-07-01 18:13:06,907 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureProcessEntity.findFeatureProcessEntitiesByFeatureCategoryKeyInAndStatusOrderByFeatureCategoryKeyAscRunOrderAsc
2025-07-01 18:13:06,908 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,923 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureProcessRepository.
2025-07-01 18:13:06,932 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureInfoRepository'
2025-07-01 18:13:06,933 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#3'
2025-07-01 18:13:06,936 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureInfoRepository.fragments#0'
2025-07-01 18:13:06,939 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,951 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureInfoRepository…
2025-07-01 18:13:06,955 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,962 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureInfoEntity.findByFeatureGroup'
2025-07-01 18:13:06,963 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureInfoEntity.findByFeatureGroup
2025-07-01 18:13:06,963 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:06,976 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureInfoEntity.queryByFeatureGroup.count
2025-07-01 18:13:06,977 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,013 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureInfoRepository.
2025-07-01 18:13:07,015 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureCategoryConfigRepository'
2025-07-01 18:13:07,018 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#1'
2025-07-01 18:13:07,020 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureCategoryConfigRepository.fragments#0'
2025-07-01 18:13:07,020 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,028 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureCategoryConfigRepository…
2025-07-01 18:13:07,028 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,035 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryConfigEntity.findByFeatureCategoryKey'
2025-07-01 18:13:07,037 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryConfigEntity.findByFeatureCategoryKey
2025-07-01 18:13:07,038 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,040 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryConfigEntity.findFeatureCategoryConfigEntitiesByFeatureCategoryKeyIn'
2025-07-01 18:13:07,041 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryConfigEntity.findFeatureCategoryConfigEntitiesByFeatureCategoryKeyIn
2025-07-01 18:13:07,042 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,047 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureCategoryConfigRepository.
2025-07-01 18:13:07,050 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureCategoryInfoRepository'
2025-07-01 18:13:07,053 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#2'
2025-07-01 18:13:07,054 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureCategoryInfoRepository.fragments#0'
2025-07-01 18:13:07,054 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,068 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureCategoryInfoRepository…
2025-07-01 18:13:07,069 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,072 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryInfoEntity.findByFeatureCategoryKey'
2025-07-01 18:13:07,073 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryInfoEntity.findByFeatureCategoryKey
2025-07-01 18:13:07,074 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,078 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryInfoEntity.findFeatureCategoryInfoEntitiesByFeatureCategoryKeyIn'
2025-07-01 18:13:07,080 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryInfoEntity.findFeatureCategoryInfoEntitiesByFeatureCategoryKeyIn
2025-07-01 18:13:07,080 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,086 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureCategoryInfoRepository.
2025-07-01 18:13:07,089 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'x6DataParserbackup'
2025-07-01 18:13:07,091 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localJdbcTemplate'
2025-07-01 18:13:07,091 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'localJdbcTemplate' via factory method to bean named 'localDataSource'
2025-07-01 18:13:07,109 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFep'
2025-07-01 18:13:07,109 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFep' via factory method to bean named 'entityManagerFactoryBuilder'
2025-07-01 18:13:07,138 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionManagerFep'
2025-07-01 18:13:07,139 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionManagerFep' via factory method to bean named 'entityManagerFactoryBuilder'
2025-07-01 18:13:07,151 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.jpa.util.JpaMetamodelCacheCleanup'
2025-07-01 18:13:07,151 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.jpa.repository.support.JpaEvaluationContextExtension'
2025-07-01 18:13:07,155 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'data-jpa.repository-aot-processor#0'
2025-07-01 18:13:07,161 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'objectMapper'
2025-07-01 18:13:07,178 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerLocal'
2025-07-01 18:13:07,179 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerLocal' via factory method to bean named 'entityManagerFactoryBuilder'
2025-07-01 18:13:07,183 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionManageLocal'
2025-07-01 18:13:07,183 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionManageLocal' via factory method to bean named 'entityManagerFactoryBuilder'
2025-07-01 18:13:07,188 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaSharedEM_entityManagerFactoryLocal'
2025-07-01 18:13:07,193 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#4'
2025-07-01 18:13:07,196 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureProcessDefinitionRepository.fragments#0'
2025-07-01 18:13:07,196 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureProcessDefinitionRepository'
2025-07-01 18:13:07,202 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,208 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.local.FeatureProcessDefinitionRepository…
2025-07-01 18:13:07,209 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:07,215 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.local.FeatureProcessDefinitionRepository.
2025-07-01 18:13:07,217 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'data-jpa.repository-aot-processor#1'
2025-07-01 18:13:07,219 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
2025-07-01 18:13:07,219 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
2025-07-01 18:13:07,222 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration'
2025-07-01 18:13:07,223 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
2025-07-01 18:13:07,226 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-07-01 18:13:07,228 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
2025-07-01 18:13:07,228 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'fileWatcher'
2025-07-01 18:13:07,234 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sslPropertiesSslBundleRegistrar'
2025-07-01 18:13:07,235 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'sslPropertiesSslBundleRegistrar' via factory method to bean named 'fileWatcher'
2025-07-01 18:13:07,238 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sslBundleRegistry'
2025-07-01 18:13:07,247 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter'
2025-07-01 18:13:07,251 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
2025-07-01 18:13:07,251 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-07-01 18:13:07,257 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
2025-07-01 18:13:07,261 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
2025-07-01 18:13:07,261 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
2025-07-01 18:13:07,381 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-07-01 18:13:07,382 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-07-01 18:13:07,385 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-07-01 18:13:07,401 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-07-01 18:13:07,406 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
2025-07-01 18:13:07,406 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-07-01 18:13:07,406 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-07-01 18:13:07,406 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-07-01 18:13:07,411 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-07-01 18:13:07,434 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'errorPageCustomizer'
2025-07-01 18:13:07,434 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-07-01 18:13:07,437 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-07-01 18:13:07,437 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-07-01 18:13:07,437 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-07-01 18:13:07,437 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dispatcherServlet'
2025-07-01 18:13:07,437 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-07-01 18:13:07,442 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-07-01 18:13:07,452 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-07-01 18:13:07,472 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-07-01 18:13:07,472 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-07-01 18:13:07,477 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'multipartConfigElement'
2025-07-01 18:13:07,477 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-07-01 18:13:07,477 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-07-01 18:13:07,489 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-07-01 18:13:07,497 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-07-01 18:13:07,502 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
2025-07-01 18:13:07,502 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration'
2025-07-01 18:13:07,502 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'threadPoolTaskExecutorBuilder'
2025-07-01 18:13:07,507 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-07-01 18:13:07,512 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'threadPoolTaskExecutorBuilder' via factory method to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-07-01 18:13:07,517 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration'
2025-07-01 18:13:07,519 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-07-01 18:13:07,520 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'simpleAsyncTaskExecutorBuilder'
2025-07-01 18:13:07,524 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorConfiguration'
2025-07-01 18:13:07,525 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
2025-07-01 18:13:07,526 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
2025-07-01 18:13:07,528 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'error'
2025-07-01 18:13:07,529 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'beanNameViewResolver'
2025-07-01 18:13:07,532 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
2025-07-01 18:13:07,533 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-07-01 18:13:07,536 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-07-01 18:13:07,536 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-07-01 18:13:07,536 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'conventionErrorViewResolver'
2025-07-01 18:13:07,547 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'errorAttributes'
2025-07-01 18:13:07,555 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'basicErrorController'
2025-07-01 18:13:07,556 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
2025-07-01 18:13:07,564 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
2025-07-01 18:13:07,566 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-07-01 18:13:07,566 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-07-01 18:13:07,566 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-07-01 18:13:07,574 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
2025-07-01 18:13:07,576 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-07-01 18:13:07,576 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-07-01 18:13:07,576 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-07-01 18:13:07,579 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'openEntityManagerInViewInterceptorConfigurer'
2025-07-01 18:13:07,579 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration'
2025-07-01 18:13:07,579 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration' via constructor to bean named 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties'
2025-07-01 18:13:07,579 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'openEntityManagerInViewInterceptor'
2025-07-01 18:13:07,587 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'openEntityManagerInViewInterceptorConfigurer' via factory method to bean named 'openEntityManagerInViewInterceptor'
2025-07-01 18:13:07,590 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.web.config.SpringDataWebConfiguration'
2025-07-01 18:13:07,591 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.data.web.config.SpringDataWebConfiguration' via constructor to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-07-01 18:13:07,599 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'welcomePageHandlerMapping'
2025-07-01 18:13:07,600 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcConversionService'
2025-07-01 18:13:07,616 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcResourceUrlProvider'
2025-07-01 18:13:07,621 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-07-01 18:13:07,623 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:07,623 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-07-01 18:13:07,651 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'welcomePageNotAcceptableHandlerMapping'
2025-07-01 18:13:07,651 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-07-01 18:13:07,656 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:07,656 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-07-01 18:13:07,666 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localeResolver'
2025-07-01 18:13:07,671 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'themeResolver'
2025-07-01 18:13:07,674 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'flashMapManager'
2025-07-01 18:13:07,676 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewNameTranslator'
2025-07-01 18:13:07,681 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcValidator'
2025-07-01 18:13:07,687 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcContentNegotiationManager'
2025-07-01 18:13:07,698 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'requestMappingHandlerMapping'
2025-07-01 18:13:07,701 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
2025-07-01 18:13:07,701 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:07,702 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-07-01 18:13:07,776 DEBUG org.springframework.web.servlet.handler.AbstractHandlerMethodMapping [Test worker] 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-01 18:13:07,794 DEBUG org.springframework.web.servlet.handler.AbstractHandlerMethodMapping [Test worker] 2 mappings in 'requestMappingHandlerMapping'
2025-07-01 18:13:07,802 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcPatternParser'
2025-07-01 18:13:07,804 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcUrlPathHelper'
2025-07-01 18:13:07,806 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcPathMatcher'
2025-07-01 18:13:07,806 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewControllerHandlerMapping'
2025-07-01 18:13:07,810 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:07,810 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-07-01 18:13:07,810 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'beanNameHandlerMapping'
2025-07-01 18:13:07,810 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:07,815 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-07-01 18:13:07,820 DEBUG org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping [Test worker] 'beanNameHandlerMapping' {}
2025-07-01 18:13:07,823 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'routerFunctionMapping'
2025-07-01 18:13:07,824 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:07,825 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-07-01 18:13:07,827 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'messageConverters'
2025-07-01 18:13:07,827 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
2025-07-01 18:13:07,833 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'stringHttpMessageConverter'
2025-07-01 18:13:07,834 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
2025-07-01 18:13:07,835 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'stringHttpMessageConverter' via factory method to bean named 'environment'
2025-07-01 18:13:07,841 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
2025-07-01 18:13:07,841 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
2025-07-01 18:13:07,846 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'objectMapper'
2025-07-01 18:13:07,958 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'resourceHandlerMapping'
2025-07-01 18:13:07,960 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
2025-07-01 18:13:07,960 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:07,960 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-07-01 18:13:08,013 DEBUG org.springframework.web.servlet.handler.SimpleUrlHandlerMapping [Test worker] 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-07-01 18:13:08,015 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'defaultServletHandlerMapping'
2025-07-01 18:13:08,017 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
2025-07-01 18:13:08,018 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcContentNegotiationManager'
2025-07-01 18:13:08,019 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:08,019 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcValidator'
2025-07-01 18:13:08,043 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sortResolver'
2025-07-01 18:13:08,045 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sortCustomizer'
2025-07-01 18:13:08,046 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration'
2025-07-01 18:13:08,047 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties'
2025-07-01 18:13:08,049 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration' via constructor to bean named 'spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties'
2025-07-01 18:13:08,055 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'pageableResolver'
2025-07-01 18:13:08,058 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'pageableCustomizer'
2025-07-01 18:13:08,069 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'applicationTaskExecutor'
2025-07-01 18:13:08,070 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'threadPoolTaskExecutorBuilder'
2025-07-01 18:13:08,081 DEBUG org.springframework.scheduling.concurrent.ExecutorConfigurationSupport [Test worker] Initializing ExecutorService 'applicationTaskExecutor'
2025-07-01 18:13:08,106 DEBUG org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter [Test worker] ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-01 18:13:08,155 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcViewResolver'
2025-07-01 18:13:08,155 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mvcViewResolver' via factory method to bean named 'mvcContentNegotiationManager'
2025-07-01 18:13:08,164 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'defaultViewResolver'
2025-07-01 18:13:08,175 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewResolver'
2025-07-01 18:13:08,175 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-07-01 18:13:08,179 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewResolver'
2025-07-01 18:13:08,192 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'handlerFunctionAdapter'
2025-07-01 18:13:08,195 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcUriComponentsContributor'
2025-07-01 18:13:08,195 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'mvcConversionService'
2025-07-01 18:13:08,195 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'requestMappingHandlerAdapter'
2025-07-01 18:13:08,199 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
2025-07-01 18:13:08,200 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
2025-07-01 18:13:08,200 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'handlerExceptionResolver'
2025-07-01 18:13:08,204 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'handlerExceptionResolver' via factory method to bean named 'mvcContentNegotiationManager'
2025-07-01 18:13:08,206 DEBUG org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver [Test worker] ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-01 18:13:08,211 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'requestContextFilter'
2025-07-01 18:13:08,217 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
2025-07-01 18:13:08,218 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'formContentFilter'
2025-07-01 18:13:08,221 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration'
2025-07-01 18:13:08,222 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration'
2025-07-01 18:13:08,222 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration'
2025-07-01 18:13:08,223 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration'
2025-07-01 18:13:08,223 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'applicationAvailability'
2025-07-01 18:13:08,226 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.jta.JtaAutoConfiguration'
2025-07-01 18:13:08,227 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
2025-07-01 18:13:08,228 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
2025-07-01 18:13:08,228 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-07-01 18:13:08,239 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration'
2025-07-01 18:13:08,239 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
2025-07-01 18:13:08,239 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionExecutionListeners'
2025-07-01 18:13:08,243 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
2025-07-01 18:13:08,251 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration'
2025-07-01 18:13:08,252 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
2025-07-01 18:13:08,256 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
2025-07-01 18:13:08,258 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
2025-07-01 18:13:08,264 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
2025-07-01 18:13:08,266 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'parameterNamesModule'
2025-07-01 18:13:08,267 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
2025-07-01 18:13:08,268 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jsonMixinModule'
2025-07-01 18:13:08,272 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration'
2025-07-01 18:13:08,272 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jsonMixinModuleEntries'
2025-07-01 18:13:08,272 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jsonMixinModuleEntries' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-07-01 18:13:08,272 DEBUG org.springframework.boot.autoconfigure.AutoConfigurationPackages$BasePackages [Test worker] @EnableAutoConfiguration was declared on a class in the package 'com.qihoo.feature'. Automatic @Repository and @Entity scanning is enabled.
2025-07-01 18:13:08,295 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-07-01 18:13:08,295 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'jsonMixinModuleEntries'
2025-07-01 18:13:08,297 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jsonComponentModule'
2025-07-01 18:13:08,297 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
2025-07-01 18:13:08,300 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jacksonGeoModule'
2025-07-01 18:13:08,304 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration'
2025-07-01 18:13:08,308 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'springDataWebSettings'
2025-07-01 18:13:08,315 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'pageModule'
2025-07-01 18:13:08,320 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
2025-07-01 18:13:08,321 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
2025-07-01 18:13:08,322 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
2025-07-01 18:13:08,324 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration'
2025-07-01 18:13:08,325 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'lifecycleProcessor'
2025-07-01 18:13:08,326 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
2025-07-01 18:13:08,328 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'lifecycleProcessor' via factory method to bean named 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
2025-07-01 18:13:08,332 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
2025-07-01 18:13:08,333 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
2025-07-01 18:13:08,334 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.web.config.ProjectingArgumentResolverRegistrar'
2025-07-01 18:13:08,334 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'offsetResolver'
2025-07-01 18:13:08,343 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration'
2025-07-01 18:13:08,344 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'clientHttpRequestFactoryBuilder'
2025-07-01 18:13:08,344 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
2025-07-01 18:13:08,349 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'clientHttpRequestFactoryBuilder' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
2025-07-01 18:13:08,361 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'clientHttpRequestFactorySettings'
2025-07-01 18:13:08,361 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'clientHttpRequestFactorySettings' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
2025-07-01 18:13:08,370 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
2025-07-01 18:13:08,375 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
2025-07-01 18:13:08,376 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
2025-07-01 18:13:08,376 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration'
2025-07-01 18:13:08,381 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jdbcTemplate'
2025-07-01 18:13:08,383 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
2025-07-01 18:13:08,385 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'localDataSource'
2025-07-01 18:13:08,385 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
2025-07-01 18:13:08,392 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
2025-07-01 18:13:08,394 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration'
2025-07-01 18:13:08,394 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jdbcClient'
2025-07-01 18:13:08,395 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jdbcClient' via factory method to bean named 'localJdbcTemplate'
2025-07-01 18:13:08,398 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration'
2025-07-01 18:13:08,398 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration'
2025-07-01 18:13:08,399 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'threadPoolTaskSchedulerBuilder'
2025-07-01 18:13:08,400 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-07-01 18:13:08,404 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'threadPoolTaskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-07-01 18:13:08,406 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration'
2025-07-01 18:13:08,408 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration' via constructor to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-07-01 18:13:08,409 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'simpleAsyncTaskSchedulerBuilder'
2025-07-01 18:13:08,419 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
2025-07-01 18:13:08,419 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration'
2025-07-01 18:13:08,420 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
2025-07-01 18:13:08,421 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
2025-07-01 18:13:08,421 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionTemplate'
2025-07-01 18:13:08,425 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionTemplate' via factory method to bean named 'transactionManagerFep'
2025-07-01 18:13:08,429 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
2025-07-01 18:13:08,431 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration'
2025-07-01 18:13:08,432 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'httpMessageConvertersRestClientCustomizer'
2025-07-01 18:13:08,434 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'restClientSsl'
2025-07-01 18:13:08,434 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'restClientSsl' via factory method to bean named 'sslBundleRegistry'
2025-07-01 18:13:08,437 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'restClientBuilderConfigurer'
2025-07-01 18:13:08,441 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
2025-07-01 18:13:08,443 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'characterEncodingFilter'
2025-07-01 18:13:08,445 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'multipartResolver'
2025-07-01 18:13:08,474 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [Test worker] Starting beans in phase -2147483647
2025-07-01 18:13:08,475 DEBUG org.springframework.context.support.DefaultLifecycleProcessor [Test worker] Successfully started bean 'springBootLoggingLifecycle'
2025-07-01 18:13:08,475 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [Test worker] Starting beans in phase 1073741823
2025-07-01 18:13:08,498 DEBUG org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLogger [Test worker] 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   AopAutoConfiguration matched:
      - @ConditionalOnProperty (spring.aop.auto=true) matched (OnPropertyCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:
      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   ApplicationAvailabilityAutoConfiguration#applicationAvailability matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.availability.ApplicationAvailability; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)
      - @ConditionalOnMissingBean (types: io.r2dbc.spi.ConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)

   DataSourceInitializationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jdbc.datasource.init.DatabasePopulator' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer,org.springframework.boot.autoconfigure.sql.init.SqlR2dbcScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.TransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource' (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HibernateJpaAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean', 'jakarta.persistence.EntityManager', 'org.hibernate.engine.spi.SessionImplementor' (OnClassCondition)

   HibernateJpaConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource' (OnBeanCondition)

   HttpClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.client.ClientHttpRequestFactory' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactorySettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactorySettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (server.servlet.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'objectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: org.springframework.hateoas.server.mvc.TypeConstrainedMappingJackson2HttpMessageConverter,org.springframework.data.rest.webmvc.alps.AlpsJsonHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcClientAutoConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate; SearchStrategy: all) found a single bean 'localJdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.simple.JdbcClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource' (OnBeanCondition)

   JdbcTemplateConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#entityManagerFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#jpaVendorAdapter matched:
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.JpaVendorAdapter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration.JpaWebConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.jpa.open-in-view=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor,org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JtaAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.transaction.Transaction' (OnClassCondition)
      - @ConditionalOnProperty (spring.jta.enabled) matched (OnPropertyCondition)

   LifecycleAutoConfiguration#defaultLifecycleProcessor matched:
      - @ConditionalOnMissingBean (names: lifecycleProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'jakarta.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: jakarta.servlet.MultipartConfigElement; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   RestClientAutoConfiguration#httpMessageConvertersRestClientCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.HttpMessageConvertersRestClientCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientSsl matched:
      - @ConditionalOnBean (types: org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found bean 'sslBundleRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientSsl; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   SpringDataWebAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PageableHandlerMethodArgumentResolver', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.data.web.PageableHandlerMethodArgumentResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#pageableCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#sortCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SortHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#springDataWebSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SpringDataWebSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SqlInitializationAutoConfiguration matched:
      - @ConditionalOnProperty (spring.sql.init.enabled) matched (OnPropertyCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on SqlInitializationAutoConfiguration.SqlInitializationModeCondition.ModeIsNever @ConditionalOnProperty (spring.sql.init.mode=never) did not find property 'mode' (SqlInitializationAutoConfiguration.SqlInitializationModeCondition)

   SslAutoConfiguration#sslBundleRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.ssl.SslBundleRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.ThreadPoolTaskExecutorBuilderConfiguration#threadPoolTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskSchedulingConfigurations.ThreadPoolTaskSchedulerBuilderConfiguration#threadPoolTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a single primary bean 'transactionManagerFep' from beans 'transactionManageLocal', 'transactionManagerFep' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionManagerCustomizationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionManagerCustomizationAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#flashMapManager matched:
      - @ConditionalOnMissingBean (names: flashMapManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#localeResolver matched:
      - @ConditionalOnMissingBean (names: localeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#themeResolver matched:
      - @ConditionalOnMissingBean (names: themeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#viewNameTranslator matched:
      - @ConditionalOnMissingBean (names: viewNameTranslator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'jakarta.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   AopAutoConfiguration.ClassProxyingConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   Cache2kCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.cache2k.Cache2kBuilder' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:
      Did not match:
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.benmanes.caffeine.cache.Caffeine' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CodecsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' fepDataSource, localDataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'type' (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceCheckpointRestoreConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.crac.Resource' (OnClassCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Generic:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)

   DataSourceConfiguration.OracleUcp:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSourceImpl', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.jmx.enabled=true) found different value in property 'enabled' (OnPropertyCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.OracleUcpPoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSource', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration#transactionManager:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found beans of type 'org.springframework.transaction.TransactionManager' transactionManagerFep, transactionManageLocal (OnBeanCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   ElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.elasticsearch.ElasticsearchClient' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.repository.ElasticsearchRepository' (OnClassCondition)

   ElasticsearchRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClientBuilder' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration:
      Did not match:
         - Application is deployed as a WAR file. (OnWarDeploymentCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GraphQlAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlRSocketAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebFluxSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.JakartaWebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.cache.Caching' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper:
      Did not match:
         - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found beans of type 'com.fasterxml.jackson.databind.ObjectMapper' objectMapper (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.Message' (OnClassCondition)

   JmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.jmx.enabled=true) found different value in property 'enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JndiJtaConfiguration:
      Did not match:
         - @ConditionalOnJndi JNDI environment is not available (OnJndiCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.transaction.jta.JtaTransactionManager' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaBaseConfiguration#entityManagerFactory:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean,jakarta.persistence.EntityManagerFactory; SearchStrategy: all) found beans of type 'jakarta.persistence.EntityManagerFactory' entityManagerFactoryFep, entityManagerFactoryLocal and found beans of type 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' &entityManagerFactoryFep, &entityManagerFactoryLocal (OnBeanCondition)

   JpaBaseConfiguration#transactionManager:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found beans of type 'org.springframework.transaction.TransactionManager' transactionManagerFep, transactionManageLocal (OnBeanCondition)

   JpaBaseConfiguration.PersistenceManagedTypesConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean,jakarta.persistence.EntityManagerFactory; SearchStrategy: all) found beans of type 'jakarta.persistence.EntityManagerFactory' entityManagerFactoryFep, entityManagerFactoryLocal and found beans of type 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' &entityManagerFactoryFep, &entityManagerFactoryLocal (OnBeanCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean,org.springframework.data.jpa.repository.config.JpaRepositoryConfigExtension; SearchStrategy: all) found beans of type 'org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean' &featureProcessRepository, &featureCategoryConfigRepository, &featureCategoryInfoRepository, &featureInfoRepository, &featureProcessDefinitionRepository and found beans of type 'org.springframework.data.jpa.repository.config.JpaRepositoryConfigExtension' org.springframework.data.jpa.repository.config.JpaRepositoryConfigExtension#0 (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)
         - @ConditionalOnProperty (spring.data.jpa.repositories.enabled=true) matched (OnPropertyCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   NamedParameterJdbcTemplateConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) found beans of type 'org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations' localJdbcTemplate (OnBeanCondition)

   Neo4jAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   NettyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.netty.util.NettyRuntime' (OnClassCondition)

   OAuth2AuthorizationServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2Authorization' (OnClassCondition)

   OAuth2AuthorizationServerJwtAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2Authorization' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   PulsarAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   PulsarReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   R2dbcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.r2dbc.core.R2dbcEntityTemplate' (OnClassCondition)

   R2dbcInitializationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.r2dbc.spi.ConnectionFactory', 'org.springframework.r2dbc.connection.init.DatabasePopulator' (OnClassCondition)

   R2dbcProxyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcTransactionManagerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.r2dbc.connection.R2dbcTransactionManager' (OnClassCondition)

   RSocketGraphQlClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   RSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketRequesterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)

   RSocketServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.core.RSocketServer' (OnClassCondition)

   RSocketStrategiesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.netty.buffer.PooledByteBufAllocator' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.transport.ElasticsearchTransport' (OnClassCondition)

   ReactiveElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Mono' (OnClassCondition)

   ReactiveMultipartAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Hooks' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   Saml2RelyingPartyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryAutoConfiguration.ForwardedHeaderFilterConfiguration:
      Did not match:
         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.application.admin.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder' simpleAsyncTaskExecutorBuilder (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutorVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder' simpleAsyncTaskSchedulerBuilder (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.spring6.SpringTemplateEngine' (OnClassCondition)

   TransactionAutoConfiguration#transactionalOperator:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.AspectJTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.transaction.aspectj.AbstractTransactionAspect; SearchStrategy: all) did not find any beans of type org.springframework.transaction.aspectj.AbstractTransactionAspect (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) found beans of type 'org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration' org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   ValidationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.validation.executable.ExecutableValidator' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) did not find property 'enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ProblemDetailsErrorHandlingConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.problemdetails.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarVersionLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSessionIdResolverAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Mono' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.ee10.websocket.jakarta.server.config.JakartaWebSocketServletContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.jdbc.XADataSourceWrapper; SearchStrategy: all) did not find any beans of type org.springframework.boot.jdbc.XADataSourceWrapper (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'jakarta.transaction.TransactionManager', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration

    org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



2025-07-01 18:13:08,511 DEBUG org.springframework.boot.availability.ApplicationAvailabilityBean [Test worker] Application availability state LivenessState changed to CORRECT
2025-07-01 18:13:08,517 DEBUG org.springframework.boot.availability.ApplicationAvailabilityBean [Test worker] Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
2025-07-01 18:13:08,542 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 0, missCount = 1, failureCount = 0]
2025-07-01 18:13:08,544 DEBUG org.springframework.test.context.web.ServletTestExecutionListener [Test worker] Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.qihoo.feature.DagParseTest
2025-07-01 18:13:08,619 DEBUG org.springframework.test.context.support.DependencyInjectionTestExecutionListener [Test worker] Performing dependency injection for test class com.qihoo.feature.DagParseTest
2025-07-01 18:13:08,619 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 1, missCount = 1, failureCount = 0]
2025-07-01 18:13:08,625 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 2, missCount = 1, failureCount = 0]
2025-07-01 18:13:08,635 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] Before test method: class [DagParseTest], method [parse2Test], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-07-01 18:13:08,640 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 3, missCount = 1, failureCount = 0]
2025-07-01 18:13:08,640 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 4, missCount = 1, failureCount = 0]
2025-07-01 18:13:09,829 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 5, missCount = 1, failureCount = 0]
2025-07-01 18:13:09,837 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 6, missCount = 1, failureCount = 0]
2025-07-01 18:13:09,861 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:10,365 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:10,443 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:10,607 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-07-01 18:13:10,752 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 7, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,753 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 8, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,756 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 9, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,757 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 10, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,757 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 11, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,759 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 12, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,761 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 13, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,762 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] After test method: class [DagParseTest], method [parse2Test], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-07-01 18:13:10,762 DEBUG org.springframework.test.context.web.ServletTestExecutionListener [Test worker] Resetting RequestContextHolder for test class com.qihoo.feature.DagParseTest
2025-07-01 18:13:10,771 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 14, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,771 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@19b7852b size = 1, maxSize = 32, parentContextCount = 0, hitCount = 15, missCount = 1, failureCount = 0]
2025-07-01 18:13:10,771 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] After test class: class [DagParseTest], class annotated with @DirtiesContext [false] with mode [null]
2025-07-01 18:13:10,790 DEBUG org.springframework.context.support.AbstractApplicationContext [SpringApplicationShutdownHook] Closing org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c, started on Tue Jul 01 18:12:53 CST 2025
2025-07-01 18:13:10,794 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [SpringApplicationShutdownHook] Stopping beans in phase 1073741823
2025-07-01 18:13:10,795 DEBUG org.springframework.context.support.DefaultLifecycleProcessor [SpringApplicationShutdownHook] Bean 'applicationTaskExecutor' completed its stop procedure
2025-07-01 18:13:10,796 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [SpringApplicationShutdownHook] Stopping beans in phase -2147483647
2025-07-01 18:13:10,797 DEBUG org.springframework.context.support.DefaultLifecycleProcessor [SpringApplicationShutdownHook] Bean 'springBootLoggingLifecycle' completed its stop procedure
2025-07-01 18:13:10,798 DEBUG org.springframework.scheduling.concurrent.ExecutorConfigurationSupport [SpringApplicationShutdownHook] Shutting down ExecutorService 'applicationTaskExecutor'
