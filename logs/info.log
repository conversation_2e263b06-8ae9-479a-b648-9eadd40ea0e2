2025-06-23 17:15:00,274 INFO org.springframework.boot.StartupInfoLogger [Test worker] Started DagParseTest in 15.018 seconds (process running for 17.663)
2025-06-23 17:15:03,227 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-23 17:15:03,230 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-23 17:15:03,231 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-23 17:15:05,808 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-23 17:15:05,809 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown initiated...
2025-06-23 17:15:05,857 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown completed.
2025-06-23 17:17:18,056 INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils [Test worker] Could not detect default configuration classes for test class [com.qihoo.feature.DagParseTest]: DagParseTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-23 17:17:18,241 INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper [Test worker] Found @SpringBootConfiguration com.qihoo.feature.FeatureTransApplication for test class com.qihoo.feature.DagParseTest
2025-06-23 17:17:18,941 INFO org.springframework.boot.StartupInfoLogger [Test worker] Starting DagParseTest using Java 17.0.4 with PID 24356 (started by zouzonghui-jk in D:\dev\extcode\feature-trans)
2025-06-23 17:17:18,954 INFO org.springframework.boot.SpringApplication [Test worker] No active profile set, falling back to 1 default profile: "default"
2025-06-23 17:17:19,639 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:17:19,723 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 64 ms. Found 4 JPA repository interfaces.
2025-06-23 17:17:19,725 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:17:19,742 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 12 ms. Found 1 JPA repository interface.
2025-06-23 17:17:22,956 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:17:23,085 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Starting...
2025-06-23 17:17:28,976 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@552088cc
2025-06-23 17:17:28,979 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Start completed.
2025-06-23 17:17:30,340 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-23 17:17:30,372 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:17:30,374 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Starting...
2025-06-23 17:17:30,719 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@17d25e1d
2025-06-23 17:17:30,719 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Start completed.
2025-06-23 17:17:30,820 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-23 17:17:31,723 INFO org.springframework.data.jpa.repository.query.QueryEnhancerFactory [Test worker] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-23 17:17:33,891 INFO org.springframework.boot.StartupInfoLogger [Test worker] Started DagParseTest in 15.445 seconds (process running for 18.16)
2025-06-23 17:17:37,840 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-23 17:17:37,843 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-23 17:17:37,844 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-23 17:17:40,081 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-23 17:17:40,082 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown initiated...
2025-06-23 17:17:40,101 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown completed.
2025-06-23 17:19:04,052 INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils [Test worker] Could not detect default configuration classes for test class [com.qihoo.feature.DagParseTest]: DagParseTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-23 17:19:04,256 INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper [Test worker] Found @SpringBootConfiguration com.qihoo.feature.FeatureTransApplication for test class com.qihoo.feature.DagParseTest
2025-06-23 17:19:05,392 INFO org.springframework.boot.StartupInfoLogger [Test worker] Starting DagParseTest using Java 17.0.4 with PID 63240 (started by zouzonghui-jk in D:\dev\extcode\feature-trans)
2025-06-23 17:19:05,410 INFO org.springframework.boot.SpringApplication [Test worker] No active profile set, falling back to 1 default profile: "default"
2025-06-23 17:19:06,752 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:19:06,882 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 95 ms. Found 4 JPA repository interfaces.
2025-06-23 17:19:06,886 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:19:06,902 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 13 ms. Found 1 JPA repository interface.
2025-06-23 17:19:09,688 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:19:09,789 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Starting...
2025-06-23 17:19:15,506 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@22bfd4b
2025-06-23 17:19:15,516 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Start completed.
2025-06-23 17:19:17,684 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-23 17:19:17,738 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:19:17,741 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Starting...
2025-06-23 17:19:18,095 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2c4eda3a
2025-06-23 17:19:18,097 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Start completed.
2025-06-23 17:19:18,198 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-23 17:19:19,267 INFO org.springframework.data.jpa.repository.query.QueryEnhancerFactory [Test worker] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-23 17:20:37,219 INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils [Test worker] Could not detect default configuration classes for test class [com.qihoo.feature.DagParseTest]: DagParseTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-23 17:20:37,408 INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper [Test worker] Found @SpringBootConfiguration com.qihoo.feature.FeatureTransApplication for test class com.qihoo.feature.DagParseTest
2025-06-23 17:20:38,047 INFO org.springframework.boot.StartupInfoLogger [Test worker] Starting DagParseTest using Java 17.0.4 with PID 63448 (started by zouzonghui-jk in D:\dev\extcode\feature-trans)
2025-06-23 17:20:38,061 INFO org.springframework.boot.SpringApplication [Test worker] No active profile set, falling back to 1 default profile: "default"
2025-06-23 17:20:39,069 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:20:39,147 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 58 ms. Found 4 JPA repository interfaces.
2025-06-23 17:20:39,150 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:20:39,168 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 16 ms. Found 1 JPA repository interface.
2025-06-23 17:20:41,223 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:20:41,289 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Starting...
2025-06-23 17:20:46,913 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@57eed461
2025-06-23 17:20:46,915 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Start completed.
2025-06-23 17:20:48,123 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-23 17:20:48,176 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:20:48,177 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Starting...
2025-06-23 17:20:48,438 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@359fd0a2
2025-06-23 17:20:48,439 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Start completed.
2025-06-23 17:20:48,506 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-23 17:20:49,201 INFO org.springframework.data.jpa.repository.query.QueryEnhancerFactory [Test worker] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-23 17:20:51,103 INFO org.springframework.boot.StartupInfoLogger [Test worker] Started DagParseTest in 13.482 seconds (process running for 17.184)
2025-06-23 17:20:53,086 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-23 17:20:53,091 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-23 17:20:53,092 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-23 17:20:57,788 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-23 17:20:57,788 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown initiated...
2025-06-23 17:20:57,803 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown completed.
2025-06-23 17:23:06,621 INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils [Test worker] Could not detect default configuration classes for test class [com.qihoo.feature.DagParseTest]: DagParseTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-23 17:23:06,893 INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper [Test worker] Found @SpringBootConfiguration com.qihoo.feature.FeatureTransApplication for test class com.qihoo.feature.DagParseTest
2025-06-23 17:23:08,068 INFO org.springframework.boot.StartupInfoLogger [Test worker] Starting DagParseTest using Java 17.0.4 with PID 76760 (started by zouzonghui-jk in D:\dev\extcode\feature-trans)
2025-06-23 17:23:08,092 INFO org.springframework.boot.SpringApplication [Test worker] No active profile set, falling back to 1 default profile: "default"
2025-06-23 17:23:09,251 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:23:09,419 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 142 ms. Found 4 JPA repository interfaces.
2025-06-23 17:23:09,425 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:23:09,452 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 20 ms. Found 1 JPA repository interface.
2025-06-23 17:23:12,440 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:23:12,522 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Starting...
2025-06-23 17:23:18,284 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@21ce3b22
2025-06-23 17:23:18,287 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Start completed.
2025-06-23 17:23:19,886 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-23 17:23:19,936 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-23 17:23:19,938 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Starting...
2025-06-23 17:23:20,277 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@700596f9
2025-06-23 17:23:20,278 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Start completed.
2025-06-23 17:23:20,419 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-23 17:23:21,474 INFO org.springframework.data.jpa.repository.query.QueryEnhancerFactory [Test worker] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-23 17:23:23,503 INFO org.springframework.boot.StartupInfoLogger [Test worker] Started DagParseTest in 16.222 seconds (process running for 23.543)
2025-06-26 17:58:46,732 INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils [Test worker] Could not detect default configuration classes for test class [com.qihoo.feature.DagParseTest]: DagParseTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-26 17:58:47,044 INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper [Test worker] Found @SpringBootConfiguration com.qihoo.feature.FeatureTransApplication for test class com.qihoo.feature.DagParseTest
2025-06-26 17:58:48,208 INFO org.springframework.boot.StartupInfoLogger [Test worker] Starting DagParseTest using Java 17.0.4 with PID 42828 (started by zouzonghui-jk in D:\dev\extcode\feature-trans)
2025-06-26 17:58:48,220 INFO org.springframework.boot.SpringApplication [Test worker] No active profile set, falling back to 1 default profile: "default"
2025-06-26 17:58:49,222 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 17:58:49,318 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 69 ms. Found 4 JPA repository interfaces.
2025-06-26 17:58:49,320 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 17:58:49,341 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 16 ms. Found 1 JPA repository interface.
2025-06-26 17:58:51,489 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-26 17:58:51,573 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Starting...
2025-06-26 17:58:57,253 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@523ade68
2025-06-26 17:58:57,255 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Start completed.
2025-06-26 17:58:58,567 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-26 17:58:58,605 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-26 17:58:58,608 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Starting...
2025-06-26 17:58:59,817 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-26 17:59:00,547 INFO org.springframework.data.jpa.repository.query.QueryEnhancerFactory [Test worker] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-26 17:59:02,343 INFO org.springframework.boot.StartupInfoLogger [Test worker] Started DagParseTest in 14.916 seconds (process running for 18.25)
2025-06-26 17:59:05,145 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-26 17:59:05,148 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-26 17:59:05,149 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-26 17:59:08,200 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-07-01 18:12:52,289 INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils [Test worker] Could not detect default configuration classes for test class [com.qihoo.feature.DagParseTest]: DagParseTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-07-01 18:12:52,479 INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper [Test worker] Found @SpringBootConfiguration com.qihoo.feature.FeatureTransApplication for test class com.qihoo.feature.DagParseTest
2025-07-01 18:12:53,235 INFO org.springframework.boot.StartupInfoLogger [Test worker] Starting DagParseTest using Java 17.0.4 with PID 46360 (started by zouzonghui-jk in D:\dev\extcode\feature-trans)
2025-07-01 18:12:53,246 INFO org.springframework.boot.SpringApplication [Test worker] No active profile set, falling back to 1 default profile: "default"
2025-07-01 18:12:54,405 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-01 18:12:54,559 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 110 ms. Found 4 JPA repository interfaces.
2025-07-01 18:12:54,559 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-01 18:12:54,587 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 20 ms. Found 1 JPA repository interface.
2025-07-01 18:12:57,852 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-01 18:12:57,972 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Starting...
2025-07-01 18:13:03,812 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@570299e3
2025-07-01 18:13:03,814 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Start completed.
2025-07-01 18:13:05,546 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-07-01 18:13:05,594 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-01 18:13:05,596 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Starting...
2025-07-01 18:13:06,143 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6037748a
2025-07-01 18:13:06,143 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Start completed.
2025-07-01 18:13:06,288 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-07-01 18:13:07,007 INFO org.springframework.data.jpa.repository.query.QueryEnhancerFactory [Test worker] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-01 18:13:08,507 INFO org.springframework.boot.StartupInfoLogger [Test worker] Started DagParseTest in 15.763 seconds (process running for 18.977)
2025-07-01 18:13:10,801 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-07-01 18:13:10,805 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-07-01 18:13:10,805 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-07-01 18:13:14,659 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-07-01 18:13:14,659 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown initiated...
2025-07-01 18:13:14,678 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-2 - Shutdown completed.
