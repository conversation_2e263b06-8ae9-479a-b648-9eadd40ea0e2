2025-07-01 18:13:03,924 WARN org.hibernate.dialect.Dialect [Test worker] HHH000511: The 5.6.51 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
2025-07-01 18:13:03,939 WARN org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl [Test worker] HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-01 18:13:06,228 WARN org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl [Test worker] HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-01 18:13:07,579 WARN org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration [Test worker] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
