2025-06-26 17:58:46,625 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Neither @ContextConfiguration nor @ContextHierarchy found for test class [DagParseTest]: using SpringBootContextLoader
2025-06-26 17:58:46,729 DEBUG org.springframework.test.context.support.AbstractContextLoader [Test worker] Could not detect default resource locations for test class [com.qihoo.feature.DagParseTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-06-26 17:58:46,732 INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils [Test worker] Could not detect default configuration classes for test class [com.qihoo.feature.DagParseTest]: DagParseTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-26 17:58:46,779 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Using ContextCustomizerFactory implementations for test class [DagParseTest]: [OnFailureConditionReportContextCustomizerFactory, OverrideAutoConfigurationContextCustomizerFactory, ObservabilityContextCustomizerFactory, TypeExcludeFiltersContextCustomizerFactory, PropertyMappingContextCustomizerFactory, WebDriverContextCustomizerFactory, ImportsContextCustomizerFactory, ExcludeFilterContextCustomizerFactory, HttpGraphQlTesterContextCustomizerFactory, DuplicateJsonObjectContextCustomizerFactory, MockitoContextCustomizerFactory, TestRestTemplateContextCustomizerFactory, WebTestClientContextCustomizerFactory, DisableReactorResourceFactoryGlobalResourcesContextCustomizerFactory, BeanOverrideContextCustomizerFactory, MockServerContainerContextCustomizerFactory, DynamicPropertiesContextCustomizerFactory]
2025-06-26 17:58:46,848 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Using ContextCustomizers for test class [DagParseTest]: [OnFailureConditionReportContextCustomizer, DisableObservabilityContextCustomizer, PropertyMappingContextCustomizer, WebDriverContextCustomizer, ExcludeFilterContextCustomizer, DuplicateJsonObjectContextCustomizer, MockitoContextCustomizer, TestRestTemplateContextCustomizer, DisableReactorResourceFactoryGlobalResourcesContextCustomizerCustomizer, DynamicPropertiesContextCustomizer]
2025-06-26 17:58:47,039 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\FeatureTransApplication.class]
2025-06-26 17:58:47,044 INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper [Test worker] Found @SpringBootConfiguration com.qihoo.feature.FeatureTransApplication for test class com.qihoo.feature.DagParseTest
2025-06-26 17:58:47,298 DEBUG org.springframework.test.context.util.TestContextFailureHandler [Test worker] Skipping candidate TestExecutionListener [org.springframework.test.context.observation.MicrometerObservationRegistryTestExecutionListener] due to a missing dependency. Specify custom TestExecutionListener classes or make the default TestExecutionListener classes and their required dependencies available. Offending class: io.micrometer.context.ThreadLocalAccessor. MicrometerObservationRegistryTestExecutionListener requires io.micrometer:micrometer-observation:1.10.8 or higher and io.micrometer:context-propagation:1.0.3 or higher.
2025-06-26 17:58:47,328 DEBUG org.springframework.test.context.support.AbstractTestContextBootstrapper [Test worker] Using TestExecutionListeners for test class [DagParseTest]: [ServletTestExecutionListener, DirtiesContextBeforeModesTestExecutionListener, ApplicationEventsTestExecutionListener, MockitoTestExecutionListener, BeanOverrideTestExecutionListener, DependencyInjectionTestExecutionListener, DirtiesContextTestExecutionListener, CommonCachesTestExecutionListener, TransactionalTestExecutionListener, SqlScriptsTestExecutionListener, EventPublishingTestExecutionListener, RestDocsTestExecutionListener, MockRestServiceServerResetTestExecutionListener, MockMvcPrintOnlyOnFailureTestExecutionListener, WebDriverTestExecutionListener, MockWebServiceServerTestExecutionListener, ResetMocksTestExecutionListener, MockitoResetTestExecutionListener]
2025-06-26 17:58:47,335 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] Before test class: class [DagParseTest], class annotated with @DirtiesContext [false] with mode [null]
2025-06-26 17:58:48,208 INFO org.springframework.boot.StartupInfoLogger [Test worker] Starting DagParseTest using Java 17.0.4 with PID 42828 (started by zouzonghui-jk in D:\dev\extcode\feature-trans)
2025-06-26 17:58:48,217 DEBUG org.springframework.boot.StartupInfoLogger [Test worker] Running with Spring Boot v3.4.1, Spring v6.2.1
2025-06-26 17:58:48,220 INFO org.springframework.boot.SpringApplication [Test worker] No active profile set, falling back to 1 default profile: "default"
2025-06-26 17:58:48,222 DEBUG org.springframework.boot.SpringApplication [Test worker] Loading source class com.qihoo.feature.FeatureTransApplication
2025-06-26 17:58:48,294 DEBUG org.springframework.context.support.AbstractApplicationContext [Test worker] Refreshing org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c
2025-06-26 17:58:48,333 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-06-26 17:58:48,361 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-06-26 17:58:48,443 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\DataSourceConfig.class]
2025-06-26 17:58:48,446 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\DateColumnMapRowMapper.class]
2025-06-26 17:58:48,455 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\FepDataSourceConfig.class]
2025-06-26 17:58:48,457 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\JacksonConfig.class]
2025-06-26 17:58:48,459 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\config\LocalDataSourceConfig.class]
2025-06-26 17:58:48,536 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Ignored because not a concrete top-level class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\local\FeatureProcessDefinitionRepository.class]
2025-06-26 17:58:48,540 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\service\X6DataParser.class]
2025-06-26 17:58:49,222 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 17:58:49,244 DEBUG org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Scanning for JPA repositories in packages com.qihoo.feature.repository.fep.
2025-06-26 17:58:49,254 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\fep] does not exist
2025-06-26 17:58:49,258 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureCategoryConfigRepository.class]
2025-06-26 17:58:49,259 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureCategoryInfoRepository.class]
2025-06-26 17:58:49,260 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureInfoRepository.class]
2025-06-26 17:58:49,261 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\fep\FeatureProcessRepository.class]
2025-06-26 17:58:49,296 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*Impl.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\fep] does not exist
2025-06-26 17:58:49,318 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 69 ms. Found 4 JPA repository interfaces.
2025-06-26 17:58:49,320 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 17:58:49,323 DEBUG org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Scanning for JPA repositories in packages com.qihoo.feature.repository.local.
2025-06-26 17:58:49,325 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\local] does not exist
2025-06-26 17:58:49,328 DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider [Test worker] Identified candidate component class: file [D:\dev\extcode\feature-trans\build\classes\java\main\com\qihoo\feature\repository\local\FeatureProcessDefinitionRepository.class]
2025-06-26 17:58:49,337 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*Impl.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\repository\local] does not exist
2025-06-26 17:58:49,341 INFO org.springframework.data.repository.config.RepositoryConfigurationDelegate [Test worker] Finished Spring Data repository scanning in 16 ms. Found 1 JPA repository interface.
2025-06-26 17:58:50,023 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-06-26 17:58:50,038 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.test.mock.mockito.MockitoPostProcessor'
2025-06-26 17:58:50,045 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'emBeanDefinitionRegistrarPostProcessor'
2025-06-26 17:58:50,046 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor'
2025-06-26 17:58:50,122 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-06-26 17:58:50,126 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-06-26 17:58:50,129 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-06-26 17:58:50,133 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-06-26 17:58:50,139 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor'
2025-06-26 17:58:50,142 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-06-26 17:58:50,145 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-06-26 17:58:50,153 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.context.annotation.internalPersistenceAnnotationProcessor'
2025-06-26 17:58:50,154 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-06-26 17:58:50,155 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-06-26 17:58:50,160 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-06-26 17:58:50,197 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-06-26 17:58:50,203 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-06-26 17:58:50,210 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-06-26 17:58:50,212 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-06-26 17:58:50,213 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'projectingArgumentResolverBeanPostProcessor'
2025-06-26 17:58:50,216 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-06-26 17:58:50,217 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-06-26 17:58:50,252 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionAttributeSource'
2025-06-26 17:58:50,254 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionInterceptor'
2025-06-26 17:58:50,255 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-06-26 17:58:50,266 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-06-26 17:58:50,267 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-06-26 17:58:50,310 DEBUG org.springframework.ui.context.support.UiApplicationContextUtils [Test worker] Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@3ef3f661]
2025-06-26 17:58:50,311 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.test.context.support.internalDynamicPropertyRegistrarBeanInitializer'
2025-06-26 17:58:50,316 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dataSourceScriptDatabaseInitializer'
2025-06-26 17:58:50,317 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration'
2025-06-26 17:58:50,320 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localDataSource'
2025-06-26 17:58:50,320 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dataSourceConfig'
2025-06-26 17:58:50,373 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
2025-06-26 17:58:50,458 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
2025-06-26 17:58:50,465 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'localDataSource'
2025-06-26 17:58:50,466 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
2025-06-26 17:58:50,477 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFactoryFep'
2025-06-26 17:58:50,478 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'fepDataSourceConfig'
2025-06-26 17:58:50,484 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'fepDataSource'
2025-06-26 17:58:50,513 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties'
2025-06-26 17:58:50,523 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties'
2025-06-26 17:58:50,527 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFactoryBuilder'
2025-06-26 17:58:50,528 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration'
2025-06-26 17:58:50,536 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'localDataSource'
2025-06-26 17:58:50,536 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties'
2025-06-26 17:58:50,538 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-06-26 17:58:50,539 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' via constructor to bean named 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties'
2025-06-26 17:58:50,546 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
2025-06-26 17:58:50,547 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
2025-06-26 17:58:50,561 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaVendorAdapter'
2025-06-26 17:58:50,595 DEBUG org.jboss.logging.LoggerProviders [Test worker] Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-06-26 17:58:50,603 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFactoryBuilder' via factory method to bean named 'jpaVendorAdapter'
2025-06-26 17:58:50,621 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFactoryFep' via factory method to bean named 'entityManagerFactoryBuilder'
2025-06-26 17:58:50,647 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\entity\fep] does not exist
2025-06-26 17:58:50,662 DEBUG org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean [Test worker] Building JPA container EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-26 17:58:51,489 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-26 17:58:51,573 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Starting...
2025-06-26 17:58:57,253 INFO com.zaxxer.hikari.pool.HikariPool [Test worker] HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@523ade68
2025-06-26 17:58:57,255 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-1 - Start completed.
2025-06-26 17:58:57,367 WARN org.hibernate.dialect.Dialect [Test worker] HHH000511: The 5.6.51 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
2025-06-26 17:58:57,377 WARN org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl [Test worker] HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-26 17:58:58,567 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-26 17:58:58,571 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFactoryLocal'
2025-06-26 17:58:58,571 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localDataSourceConfig'
2025-06-26 17:58:58,575 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFactoryLocal' via factory method to bean named 'entityManagerFactoryBuilder'
2025-06-26 17:58:58,577 DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver [Test worker] Skipping search for files matching pattern [**/*.class]: directory [D:\dev\extcode\feature-trans\build\classes\java\test\com\qihoo\feature\entity\local] does not exist
2025-06-26 17:58:58,580 DEBUG org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean [Test worker] Building JPA container EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-26 17:58:58,605 INFO org.springframework.orm.jpa.persistenceunit.SpringPersistenceUnitInfo [Test worker] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-26 17:58:58,608 INFO com.zaxxer.hikari.HikariDataSource [Test worker] HikariPool-2 - Starting...
2025-06-26 17:58:59,623 WARN org.hibernate.engine.jdbc.spi.SqlExceptionHelper [Test worker] SQL Error: 0, SQLState: 08S01
2025-06-26 17:58:59,623 ERROR org.hibernate.engine.jdbc.spi.SqlExceptionHelper [Test worker] Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-06-26 17:58:59,697 WARN org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator [Test worker] HHH000342: Could not obtain connection to query metadata
org.hibernate.exception.JDBCConnectionException: unable to obtain isolated JDBC connection [Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:100) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:116) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:320) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:129) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:81) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:226) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:194) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1431) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1502) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66) ~[spring-orm-6.2.1.jar:6.2.1]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390) ~[spring-orm-6.2.1.jar:6.2.1]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419) ~[spring-orm-6.2.1.jar:6.2.1]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400) ~[spring-orm-6.2.1.jar:6.2.1]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366) ~[spring-orm-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1855) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:289) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137) ~[spring-boot-test-3.4.1.jar:3.4.1]
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58) ~[spring-core-6.2.1.jar:6.2.1]
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46) ~[spring-core-6.2.1.jar:6.2.1]
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1461) ~[spring-boot-3.4.1.jar:3.4.1]
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:553) ~[spring-boot-test-3.4.1.jar:3.4.1]
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137) ~[spring-boot-test-3.4.1.jar:3.4.1]
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108) ~[spring-boot-test-3.4.1.jar:3.4.1]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225) ~[spring-test-6.2.1.jar:6.2.1]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152) ~[spring-test-6.2.1.jar:6.2.1]
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130) ~[spring-test-6.2.1.jar:6.2.1]
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:191) ~[spring-test-6.2.1.jar:6.2.1]
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:130) ~[spring-test-6.2.1.jar:6.2.1]
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260) ~[spring-test-6.2.1.jar:6.2.1]
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:160) ~[spring-test-6.2.1.jar:6.2.1]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$11(ClassBasedTestDescriptor.java:378) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:383) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$12(ClassBasedTestDescriptor.java:378) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197) ~[?:?]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[?:?]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[?:?]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[?:?]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:377) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$7(ClassBasedTestDescriptor.java:290) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:289) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:279) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at java.base/java.util.Optional.orElseGet(Optional.java:364) ~[?:?]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$6(ClassBasedTestDescriptor.java:278) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$1(TestMethodTestDescriptor.java:105) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:104) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:68) ~[junit-jupiter-engine-5.11.4.jar:5.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:128) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:128) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54) ~[junit-platform-engine-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198) ~[junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169) ~[junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93) ~[junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58) ~[junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141) [junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57) [junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103) [junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85) [junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47) [junit-platform-launcher-1.11.4.jar:1.11.4]
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124) [gradle-testing-junit-platform-8.11.1.jar:8.11.1]
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99) [gradle-testing-junit-platform-8.11.1.jar:8.11.1]
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94) [gradle-testing-junit-platform-8.11.1.jar:8.11.1]
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63) [gradle-testing-base-infrastructure-8.11.1.jar:8.11.1]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[?:?]
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36) [gradle-messaging-8.11.1.jar:8.11.1]
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24) [gradle-messaging-8.11.1.jar:8.11.1]
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33) [gradle-messaging-8.11.1.jar:8.11.1]
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92) [gradle-messaging-8.11.1.jar:8.11.1]
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source) [?:?]
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200) [gradle-testing-base-infrastructure-8.11.1.jar:8.11.1]
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132) [gradle-testing-base-infrastructure-8.11.1.jar:8.11.1]
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103) [gradle-testing-base-infrastructure-8.11.1.jar:8.11.1]
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63) [gradle-testing-base-infrastructure-8.11.1.jar:8.11.1]
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56) [gradle-worker-main-8.11.1.jar:8.11.1]
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121) [gradle-worker-main-8.11.1.jar:8.11.1]
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71) [gradle-worker-main-8.11.1.jar:8.11.1]
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69) [gradle-worker.jar:?]
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74) [gradle-worker.jar:?]
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:165) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:55) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:837) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111) ~[HikariCP-5.1.0.jar:?]
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:467) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	... 129 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:?]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77) ~[?:?]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480) ~[?:?]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:79) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:142) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:961) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111) ~[HikariCP-5.1.0.jar:?]
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:467) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	... 129 more
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597) ~[?:?]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.base/java.net.Socket.connect(Socket.java:633) ~[?:?]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:144) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:53) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:142) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:961) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98) ~[HikariCP-5.1.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111) ~[HikariCP-5.1.0.jar:?]
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:467) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61) ~[hibernate-core-6.6.4.Final.jar:6.6.4.Final]
	... 129 more
2025-06-26 17:58:59,716 WARN org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl [Test worker] HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-26 17:58:59,817 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [Test worker] Initialized JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-26 17:58:59,818 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureTransApplication'
2025-06-26 17:58:59,820 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dateColumnMapRowMapper'
2025-06-26 17:58:59,821 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jacksonConfig'
2025-06-26 17:58:59,822 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'x6DataParser'
2025-06-26 17:58:59,827 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureProcessRepository'
2025-06-26 17:58:59,838 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#2'
2025-06-26 17:58:59,844 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureProcessRepository.fragments#0'
2025-06-26 17:58:59,851 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaSharedEM_entityManagerFactoryFep'
2025-06-26 17:58:59,887 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaMappingContext'
2025-06-26 17:58:59,888 DEBUG org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean [Test worker] Initializing JpaMetamodelMappingContext…
2025-06-26 17:58:59,931 DEBUG org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean [Test worker] Finished initializing JpaMetamodelMappingContext
2025-06-26 17:58:59,990 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,079 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,177 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureProcessRepository…
2025-06-26 17:59:00,207 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,300 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureProcessEntity.findByFeatureCategoryKeyAndStatus'
2025-06-26 17:59:00,308 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureProcessEntity.findByFeatureCategoryKeyAndStatus
2025-06-26 17:59:00,318 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,432 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureProcessEntity.findFeatureProcessEntitiesByFeatureCategoryKeyInAndStatusOrderByFeatureCategoryKeyAscRunOrderAsc'
2025-06-26 17:59:00,434 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureProcessEntity.findFeatureProcessEntitiesByFeatureCategoryKeyInAndStatusOrderByFeatureCategoryKeyAscRunOrderAsc
2025-06-26 17:59:00,434 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,450 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureProcessRepository.
2025-06-26 17:59:00,456 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureInfoRepository'
2025-06-26 17:59:00,459 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#3'
2025-06-26 17:59:00,461 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureInfoRepository.fragments#0'
2025-06-26 17:59:00,466 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,483 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureInfoRepository…
2025-06-26 17:59:00,484 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,489 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureInfoEntity.findByFeatureGroup'
2025-06-26 17:59:00,490 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureInfoEntity.findByFeatureGroup
2025-06-26 17:59:00,492 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,509 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureInfoEntity.queryByFeatureGroup.count
2025-06-26 17:59:00,512 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,547 INFO org.springframework.data.jpa.repository.query.QueryEnhancerFactory [Test worker] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-26 17:59:00,555 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureInfoRepository.
2025-06-26 17:59:00,557 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureCategoryConfigRepository'
2025-06-26 17:59:00,560 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#1'
2025-06-26 17:59:00,561 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureCategoryConfigRepository.fragments#0'
2025-06-26 17:59:00,565 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,583 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureCategoryConfigRepository…
2025-06-26 17:59:00,585 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,589 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryConfigEntity.findByFeatureCategoryKey'
2025-06-26 17:59:00,592 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryConfigEntity.findByFeatureCategoryKey
2025-06-26 17:59:00,594 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,597 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryConfigEntity.findFeatureCategoryConfigEntitiesByFeatureCategoryKeyIn'
2025-06-26 17:59:00,600 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryConfigEntity.findFeatureCategoryConfigEntitiesByFeatureCategoryKeyIn
2025-06-26 17:59:00,602 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,610 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureCategoryConfigRepository.
2025-06-26 17:59:00,612 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureCategoryInfoRepository'
2025-06-26 17:59:00,615 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#0'
2025-06-26 17:59:00,617 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureCategoryInfoRepository.fragments#0'
2025-06-26 17:59:00,619 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,638 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.fep.FeatureCategoryInfoRepository…
2025-06-26 17:59:00,639 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,642 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryInfoEntity.findByFeatureCategoryKey'
2025-06-26 17:59:00,643 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryInfoEntity.findByFeatureCategoryKey
2025-06-26 17:59:00,644 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,653 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Looking up named query 'FeatureCategoryInfoEntity.findFeatureCategoryInfoEntitiesByFeatureCategoryKeyIn'
2025-06-26 17:59:00,654 DEBUG org.springframework.data.jpa.repository.query.NamedQuery [Test worker] Did not find named query FeatureCategoryInfoEntity.findFeatureCategoryInfoEntitiesByFeatureCategoryKeyIn
2025-06-26 17:59:00,654 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,660 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.fep.FeatureCategoryInfoRepository.
2025-06-26 17:59:00,662 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localJdbcTemplate'
2025-06-26 17:59:00,665 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'localJdbcTemplate' via factory method to bean named 'localDataSource'
2025-06-26 17:59:00,678 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerFep'
2025-06-26 17:59:00,679 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerFep' via factory method to bean named 'entityManagerFactoryBuilder'
2025-06-26 17:59:00,718 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionManagerFep'
2025-06-26 17:59:00,719 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionManagerFep' via factory method to bean named 'entityManagerFactoryBuilder'
2025-06-26 17:59:00,735 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.jpa.util.JpaMetamodelCacheCleanup'
2025-06-26 17:59:00,736 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.jpa.repository.support.JpaEvaluationContextExtension'
2025-06-26 17:59:00,739 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'data-jpa.repository-aot-processor#0'
2025-06-26 17:59:00,749 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'objectMapper'
2025-06-26 17:59:00,769 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'entityManagerLocal'
2025-06-26 17:59:00,770 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'entityManagerLocal' via factory method to bean named 'entityManagerFactoryBuilder'
2025-06-26 17:59:00,777 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionManageLocal'
2025-06-26 17:59:00,778 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionManageLocal' via factory method to bean named 'entityManagerFactoryBuilder'
2025-06-26 17:59:00,780 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpaSharedEM_entityManagerFactoryLocal'
2025-06-26 17:59:00,788 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.named-queries#4'
2025-06-26 17:59:00,790 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jpa.FeatureProcessDefinitionRepository.fragments#0'
2025-06-26 17:59:00,790 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'featureProcessDefinitionRepository'
2025-06-26 17:59:00,794 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,804 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Initializing repository instance for com.qihoo.feature.repository.local.FeatureProcessDefinitionRepository…
2025-06-26 17:59:00,805 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:00,810 DEBUG org.springframework.data.repository.core.support.RepositoryFactorySupport [Test worker] Finished creation of repository instance for com.qihoo.feature.repository.local.FeatureProcessDefinitionRepository.
2025-06-26 17:59:00,812 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'data-jpa.repository-aot-processor#1'
2025-06-26 17:59:00,813 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
2025-06-26 17:59:00,815 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
2025-06-26 17:59:00,816 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration'
2025-06-26 17:59:00,819 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
2025-06-26 17:59:00,823 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-06-26 17:59:00,823 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
2025-06-26 17:59:00,826 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'fileWatcher'
2025-06-26 17:59:00,828 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sslPropertiesSslBundleRegistrar'
2025-06-26 17:59:00,829 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'sslPropertiesSslBundleRegistrar' via factory method to bean named 'fileWatcher'
2025-06-26 17:59:00,835 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sslBundleRegistry'
2025-06-26 17:59:00,844 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter'
2025-06-26 17:59:00,847 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
2025-06-26 17:59:00,848 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-06-26 17:59:00,852 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
2025-06-26 17:59:00,853 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
2025-06-26 17:59:00,854 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
2025-06-26 17:59:00,979 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-06-26 17:59:00,980 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-06-26 17:59:00,982 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-26 17:59:00,997 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-26 17:59:01,001 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
2025-06-26 17:59:01,002 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-26 17:59:01,004 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-06-26 17:59:01,005 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-06-26 17:59:01,005 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-26 17:59:01,032 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'errorPageCustomizer'
2025-06-26 17:59:01,033 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-06-26 17:59:01,034 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-26 17:59:01,035 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-06-26 17:59:01,036 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-06-26 17:59:01,038 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'dispatcherServlet'
2025-06-26 17:59:01,038 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-06-26 17:59:01,040 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-26 17:59:01,048 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-26 17:59:01,065 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-06-26 17:59:01,066 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-26 17:59:01,069 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'multipartConfigElement'
2025-06-26 17:59:01,070 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-06-26 17:59:01,070 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-06-26 17:59:01,077 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-06-26 17:59:01,089 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-06-26 17:59:01,110 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
2025-06-26 17:59:01,111 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration'
2025-06-26 17:59:01,113 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'threadPoolTaskExecutorBuilder'
2025-06-26 17:59:01,114 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-06-26 17:59:01,118 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'threadPoolTaskExecutorBuilder' via factory method to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-06-26 17:59:01,139 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration'
2025-06-26 17:59:01,140 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-06-26 17:59:01,141 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'simpleAsyncTaskExecutorBuilder'
2025-06-26 17:59:01,144 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorConfiguration'
2025-06-26 17:59:01,145 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
2025-06-26 17:59:01,145 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
2025-06-26 17:59:01,147 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'error'
2025-06-26 17:59:01,149 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'beanNameViewResolver'
2025-06-26 17:59:01,155 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
2025-06-26 17:59:01,157 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-06-26 17:59:01,166 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-06-26 17:59:01,166 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-06-26 17:59:01,167 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'conventionErrorViewResolver'
2025-06-26 17:59:01,173 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'errorAttributes'
2025-06-26 17:59:01,177 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'basicErrorController'
2025-06-26 17:59:01,177 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
2025-06-26 17:59:01,186 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
2025-06-26 17:59:01,187 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-26 17:59:01,188 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-06-26 17:59:01,188 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-06-26 17:59:01,195 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
2025-06-26 17:59:01,198 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-06-26 17:59:01,199 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-26 17:59:01,208 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-06-26 17:59:01,212 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'openEntityManagerInViewInterceptorConfigurer'
2025-06-26 17:59:01,215 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration'
2025-06-26 17:59:01,220 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration' via constructor to bean named 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties'
2025-06-26 17:59:01,223 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'openEntityManagerInViewInterceptor'
2025-06-26 17:59:01,224 WARN org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration [Test worker] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 17:59:01,230 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'openEntityManagerInViewInterceptorConfigurer' via factory method to bean named 'openEntityManagerInViewInterceptor'
2025-06-26 17:59:01,250 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.web.config.SpringDataWebConfiguration'
2025-06-26 17:59:01,252 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.data.web.config.SpringDataWebConfiguration' via constructor to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-06-26 17:59:01,258 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'welcomePageHandlerMapping'
2025-06-26 17:59:01,259 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcConversionService'
2025-06-26 17:59:01,281 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcResourceUrlProvider'
2025-06-26 17:59:01,286 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-06-26 17:59:01,287 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,287 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-06-26 17:59:01,330 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'welcomePageNotAcceptableHandlerMapping'
2025-06-26 17:59:01,330 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-06-26 17:59:01,331 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,331 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-06-26 17:59:01,340 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'localeResolver'
2025-06-26 17:59:01,343 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'themeResolver'
2025-06-26 17:59:01,353 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'flashMapManager'
2025-06-26 17:59:01,367 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewNameTranslator'
2025-06-26 17:59:01,372 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcValidator'
2025-06-26 17:59:01,377 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcContentNegotiationManager'
2025-06-26 17:59:01,388 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'requestMappingHandlerMapping'
2025-06-26 17:59:01,390 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
2025-06-26 17:59:01,391 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,391 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-06-26 17:59:01,475 DEBUG org.springframework.web.servlet.handler.AbstractHandlerMethodMapping [Test worker] 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-06-26 17:59:01,485 DEBUG org.springframework.web.servlet.handler.AbstractHandlerMethodMapping [Test worker] 2 mappings in 'requestMappingHandlerMapping'
2025-06-26 17:59:01,495 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcPatternParser'
2025-06-26 17:59:01,501 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcUrlPathHelper'
2025-06-26 17:59:01,503 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcPathMatcher'
2025-06-26 17:59:01,505 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewControllerHandlerMapping'
2025-06-26 17:59:01,507 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,507 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-06-26 17:59:01,509 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'beanNameHandlerMapping'
2025-06-26 17:59:01,512 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,513 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-06-26 17:59:01,540 DEBUG org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping [Test worker] 'beanNameHandlerMapping' {}
2025-06-26 17:59:01,544 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'routerFunctionMapping'
2025-06-26 17:59:01,546 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,546 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-06-26 17:59:01,556 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'messageConverters'
2025-06-26 17:59:01,557 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
2025-06-26 17:59:01,561 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'stringHttpMessageConverter'
2025-06-26 17:59:01,562 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
2025-06-26 17:59:01,563 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'stringHttpMessageConverter' via factory method to bean named 'environment'
2025-06-26 17:59:01,570 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
2025-06-26 17:59:01,571 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
2025-06-26 17:59:01,572 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'objectMapper'
2025-06-26 17:59:01,689 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'resourceHandlerMapping'
2025-06-26 17:59:01,691 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
2025-06-26 17:59:01,692 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,692 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-06-26 17:59:01,722 DEBUG org.springframework.web.servlet.handler.SimpleUrlHandlerMapping [Test worker] 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-06-26 17:59:01,726 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'defaultServletHandlerMapping'
2025-06-26 17:59:01,727 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
2025-06-26 17:59:01,729 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcContentNegotiationManager'
2025-06-26 17:59:01,729 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,730 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcValidator'
2025-06-26 17:59:01,753 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sortResolver'
2025-06-26 17:59:01,757 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'sortCustomizer'
2025-06-26 17:59:01,758 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration'
2025-06-26 17:59:01,759 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties'
2025-06-26 17:59:01,761 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration' via constructor to bean named 'spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties'
2025-06-26 17:59:01,768 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'pageableResolver'
2025-06-26 17:59:01,772 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'pageableCustomizer'
2025-06-26 17:59:01,784 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'applicationTaskExecutor'
2025-06-26 17:59:01,787 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'threadPoolTaskExecutorBuilder'
2025-06-26 17:59:01,801 DEBUG org.springframework.scheduling.concurrent.ExecutorConfigurationSupport [Test worker] Initializing ExecutorService 'applicationTaskExecutor'
2025-06-26 17:59:01,826 DEBUG org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter [Test worker] ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-26 17:59:01,879 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcViewResolver'
2025-06-26 17:59:01,880 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mvcViewResolver' via factory method to bean named 'mvcContentNegotiationManager'
2025-06-26 17:59:01,886 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'defaultViewResolver'
2025-06-26 17:59:01,898 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewResolver'
2025-06-26 17:59:01,901 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@2935fd2c'
2025-06-26 17:59:01,904 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'viewResolver'
2025-06-26 17:59:01,916 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'handlerFunctionAdapter'
2025-06-26 17:59:01,920 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'mvcUriComponentsContributor'
2025-06-26 17:59:01,920 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'mvcConversionService'
2025-06-26 17:59:01,921 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'requestMappingHandlerAdapter'
2025-06-26 17:59:01,924 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
2025-06-26 17:59:01,925 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
2025-06-26 17:59:01,926 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'handlerExceptionResolver'
2025-06-26 17:59:01,927 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'handlerExceptionResolver' via factory method to bean named 'mvcContentNegotiationManager'
2025-06-26 17:59:01,934 DEBUG org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver [Test worker] ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-26 17:59:01,938 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'requestContextFilter'
2025-06-26 17:59:01,942 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
2025-06-26 17:59:01,943 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'formContentFilter'
2025-06-26 17:59:01,947 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration'
2025-06-26 17:59:01,948 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration'
2025-06-26 17:59:01,948 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration'
2025-06-26 17:59:01,949 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration'
2025-06-26 17:59:01,950 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'applicationAvailability'
2025-06-26 17:59:01,954 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.jta.JtaAutoConfiguration'
2025-06-26 17:59:01,955 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
2025-06-26 17:59:01,955 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
2025-06-26 17:59:01,956 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-06-26 17:59:01,963 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration'
2025-06-26 17:59:01,966 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
2025-06-26 17:59:01,968 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionExecutionListeners'
2025-06-26 17:59:01,970 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
2025-06-26 17:59:01,976 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration'
2025-06-26 17:59:01,977 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
2025-06-26 17:59:01,977 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
2025-06-26 17:59:01,978 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
2025-06-26 17:59:01,986 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
2025-06-26 17:59:01,987 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'parameterNamesModule'
2025-06-26 17:59:01,988 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
2025-06-26 17:59:01,991 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jsonMixinModule'
2025-06-26 17:59:01,992 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration'
2025-06-26 17:59:01,994 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jsonMixinModuleEntries'
2025-06-26 17:59:01,996 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jsonMixinModuleEntries' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-06-26 17:59:01,997 DEBUG org.springframework.boot.autoconfigure.AutoConfigurationPackages$BasePackages [Test worker] @EnableAutoConfiguration was declared on a class in the package 'com.qihoo.feature'. Automatic @Repository and @Entity scanning is enabled.
2025-06-26 17:59:02,019 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c'
2025-06-26 17:59:02,019 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'jsonMixinModuleEntries'
2025-06-26 17:59:02,022 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jsonComponentModule'
2025-06-26 17:59:02,022 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
2025-06-26 17:59:02,028 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jacksonGeoModule'
2025-06-26 17:59:02,029 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration'
2025-06-26 17:59:02,036 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'springDataWebSettings'
2025-06-26 17:59:02,051 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'pageModule'
2025-06-26 17:59:02,056 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
2025-06-26 17:59:02,058 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
2025-06-26 17:59:02,060 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
2025-06-26 17:59:02,060 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration'
2025-06-26 17:59:02,061 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'lifecycleProcessor'
2025-06-26 17:59:02,062 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
2025-06-26 17:59:02,063 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'lifecycleProcessor' via factory method to bean named 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
2025-06-26 17:59:02,070 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
2025-06-26 17:59:02,071 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
2025-06-26 17:59:02,072 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.data.web.config.ProjectingArgumentResolverRegistrar'
2025-06-26 17:59:02,072 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'offsetResolver'
2025-06-26 17:59:02,077 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration'
2025-06-26 17:59:02,078 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'clientHttpRequestFactoryBuilder'
2025-06-26 17:59:02,080 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
2025-06-26 17:59:02,084 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'clientHttpRequestFactoryBuilder' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
2025-06-26 17:59:02,097 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'clientHttpRequestFactorySettings'
2025-06-26 17:59:02,098 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'clientHttpRequestFactorySettings' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
2025-06-26 17:59:02,103 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
2025-06-26 17:59:02,109 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
2025-06-26 17:59:02,111 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
2025-06-26 17:59:02,111 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration'
2025-06-26 17:59:02,112 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jdbcTemplate'
2025-06-26 17:59:02,116 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
2025-06-26 17:59:02,117 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'localDataSource'
2025-06-26 17:59:02,118 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
2025-06-26 17:59:02,123 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
2025-06-26 17:59:02,124 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration'
2025-06-26 17:59:02,125 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'jdbcClient'
2025-06-26 17:59:02,126 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'jdbcClient' via factory method to bean named 'localJdbcTemplate'
2025-06-26 17:59:02,128 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration'
2025-06-26 17:59:02,129 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration'
2025-06-26 17:59:02,130 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'threadPoolTaskSchedulerBuilder'
2025-06-26 17:59:02,131 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-06-26 17:59:02,136 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'threadPoolTaskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-06-26 17:59:02,138 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration'
2025-06-26 17:59:02,140 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration' via constructor to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-06-26 17:59:02,141 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'simpleAsyncTaskSchedulerBuilder'
2025-06-26 17:59:02,148 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
2025-06-26 17:59:02,149 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration'
2025-06-26 17:59:02,150 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
2025-06-26 17:59:02,150 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
2025-06-26 17:59:02,151 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'transactionTemplate'
2025-06-26 17:59:02,151 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'transactionTemplate' via factory method to bean named 'transactionManagerFep'
2025-06-26 17:59:02,155 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
2025-06-26 17:59:02,161 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration'
2025-06-26 17:59:02,166 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'httpMessageConvertersRestClientCustomizer'
2025-06-26 17:59:02,170 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'restClientSsl'
2025-06-26 17:59:02,171 DEBUG org.springframework.beans.factory.support.ConstructorResolver [Test worker] Autowiring by type from bean name 'restClientSsl' via factory method to bean named 'sslBundleRegistry'
2025-06-26 17:59:02,174 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'restClientBuilderConfigurer'
2025-06-26 17:59:02,177 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
2025-06-26 17:59:02,186 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'characterEncodingFilter'
2025-06-26 17:59:02,189 DEBUG org.springframework.beans.factory.support.DefaultSingletonBeanRegistry [Test worker] Creating shared instance of singleton bean 'multipartResolver'
2025-06-26 17:59:02,230 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [Test worker] Starting beans in phase -2147483647
2025-06-26 17:59:02,230 DEBUG org.springframework.context.support.DefaultLifecycleProcessor [Test worker] Successfully started bean 'springBootLoggingLifecycle'
2025-06-26 17:59:02,231 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [Test worker] Starting beans in phase 1073741823
2025-06-26 17:59:02,276 DEBUG org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLogger [Test worker] 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   AopAutoConfiguration matched:
      - @ConditionalOnProperty (spring.aop.auto=true) matched (OnPropertyCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:
      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   ApplicationAvailabilityAutoConfiguration#applicationAvailability matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.availability.ApplicationAvailability; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)
      - @ConditionalOnMissingBean (types: io.r2dbc.spi.ConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)

   DataSourceInitializationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jdbc.datasource.init.DatabasePopulator' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer,org.springframework.boot.autoconfigure.sql.init.SqlR2dbcScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.TransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource' (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HibernateJpaAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean', 'jakarta.persistence.EntityManager', 'org.hibernate.engine.spi.SessionImplementor' (OnClassCondition)

   HibernateJpaConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource' (OnBeanCondition)

   HttpClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.client.ClientHttpRequestFactory' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactorySettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactorySettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (server.servlet.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'objectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: org.springframework.hateoas.server.mvc.TypeConstrainedMappingJackson2HttpMessageConverter,org.springframework.data.rest.webmvc.alps.AlpsJsonHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcClientAutoConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate; SearchStrategy: all) found a single bean 'localJdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.simple.JdbcClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single primary bean 'localDataSource' from beans 'fepDataSource', 'localDataSource' (OnBeanCondition)

   JdbcTemplateConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#entityManagerFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#jpaVendorAdapter matched:
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.JpaVendorAdapter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration.JpaWebConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.jpa.open-in-view=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor,org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JtaAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.transaction.Transaction' (OnClassCondition)
      - @ConditionalOnProperty (spring.jta.enabled) matched (OnPropertyCondition)

   LifecycleAutoConfiguration#defaultLifecycleProcessor matched:
      - @ConditionalOnMissingBean (names: lifecycleProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'jakarta.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: jakarta.servlet.MultipartConfigElement; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   RestClientAutoConfiguration#httpMessageConvertersRestClientCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.HttpMessageConvertersRestClientCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientSsl matched:
      - @ConditionalOnBean (types: org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found bean 'sslBundleRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientSsl; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   SpringDataWebAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PageableHandlerMethodArgumentResolver', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.data.web.PageableHandlerMethodArgumentResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#pageableCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#sortCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SortHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#springDataWebSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SpringDataWebSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SqlInitializationAutoConfiguration matched:
      - @ConditionalOnProperty (spring.sql.init.enabled) matched (OnPropertyCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on SqlInitializationAutoConfiguration.SqlInitializationModeCondition.ModeIsNever @ConditionalOnProperty (spring.sql.init.mode=never) did not find property 'mode' (SqlInitializationAutoConfiguration.SqlInitializationModeCondition)

   SslAutoConfiguration#sslBundleRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.ssl.SslBundleRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.ThreadPoolTaskExecutorBuilderConfiguration#threadPoolTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskSchedulingConfigurations.ThreadPoolTaskSchedulerBuilderConfiguration#threadPoolTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a single primary bean 'transactionManagerFep' from beans 'transactionManageLocal', 'transactionManagerFep' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionManagerCustomizationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionManagerCustomizationAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#flashMapManager matched:
      - @ConditionalOnMissingBean (names: flashMapManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#localeResolver matched:
      - @ConditionalOnMissingBean (names: localeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#themeResolver matched:
      - @ConditionalOnMissingBean (names: themeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#viewNameTranslator matched:
      - @ConditionalOnMissingBean (names: viewNameTranslator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'jakarta.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   AopAutoConfiguration.ClassProxyingConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   Cache2kCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.cache2k.Cache2kBuilder' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:
      Did not match:
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.benmanes.caffeine.cache.Caffeine' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CodecsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' fepDataSource, localDataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'type' (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceCheckpointRestoreConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.crac.Resource' (OnClassCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Generic:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)

   DataSourceConfiguration.OracleUcp:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSourceImpl', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.jmx.enabled=true) found different value in property 'enabled' (OnPropertyCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.OracleUcpPoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSource', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration#transactionManager:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found beans of type 'org.springframework.transaction.TransactionManager' transactionManagerFep, transactionManageLocal (OnBeanCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   ElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.elasticsearch.ElasticsearchClient' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.repository.ElasticsearchRepository' (OnClassCondition)

   ElasticsearchRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClientBuilder' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration:
      Did not match:
         - Application is deployed as a WAR file. (OnWarDeploymentCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GraphQlAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlRSocketAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebFluxSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.JakartaWebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.cache.Caching' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper:
      Did not match:
         - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found beans of type 'com.fasterxml.jackson.databind.ObjectMapper' objectMapper (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.Message' (OnClassCondition)

   JmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.jmx.enabled=true) found different value in property 'enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JndiJtaConfiguration:
      Did not match:
         - @ConditionalOnJndi JNDI environment is not available (OnJndiCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.transaction.jta.JtaTransactionManager' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaBaseConfiguration#entityManagerFactory:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean,jakarta.persistence.EntityManagerFactory; SearchStrategy: all) found beans of type 'jakarta.persistence.EntityManagerFactory' entityManagerFactoryFep, entityManagerFactoryLocal and found beans of type 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' &entityManagerFactoryFep, &entityManagerFactoryLocal (OnBeanCondition)

   JpaBaseConfiguration#transactionManager:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found beans of type 'org.springframework.transaction.TransactionManager' transactionManagerFep, transactionManageLocal (OnBeanCondition)

   JpaBaseConfiguration.PersistenceManagedTypesConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean,jakarta.persistence.EntityManagerFactory; SearchStrategy: all) found beans of type 'jakarta.persistence.EntityManagerFactory' entityManagerFactoryFep, entityManagerFactoryLocal and found beans of type 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' &entityManagerFactoryFep, &entityManagerFactoryLocal (OnBeanCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean,org.springframework.data.jpa.repository.config.JpaRepositoryConfigExtension; SearchStrategy: all) found beans of type 'org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean' &featureCategoryInfoRepository, &featureCategoryConfigRepository, &featureProcessRepository, &featureInfoRepository, &featureProcessDefinitionRepository and found beans of type 'org.springframework.data.jpa.repository.config.JpaRepositoryConfigExtension' org.springframework.data.jpa.repository.config.JpaRepositoryConfigExtension#0 (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)
         - @ConditionalOnProperty (spring.data.jpa.repositories.enabled=true) matched (OnPropertyCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   NamedParameterJdbcTemplateConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) found beans of type 'org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations' localJdbcTemplate (OnBeanCondition)

   Neo4jAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   NettyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.netty.util.NettyRuntime' (OnClassCondition)

   OAuth2AuthorizationServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2Authorization' (OnClassCondition)

   OAuth2AuthorizationServerJwtAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2Authorization' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   PulsarAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   PulsarReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   R2dbcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.r2dbc.core.R2dbcEntityTemplate' (OnClassCondition)

   R2dbcInitializationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.r2dbc.spi.ConnectionFactory', 'org.springframework.r2dbc.connection.init.DatabasePopulator' (OnClassCondition)

   R2dbcProxyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcTransactionManagerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.r2dbc.connection.R2dbcTransactionManager' (OnClassCondition)

   RSocketGraphQlClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   RSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketRequesterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)

   RSocketServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.core.RSocketServer' (OnClassCondition)

   RSocketStrategiesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.netty.buffer.PooledByteBufAllocator' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.transport.ElasticsearchTransport' (OnClassCondition)

   ReactiveElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Mono' (OnClassCondition)

   ReactiveMultipartAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Hooks' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   Saml2RelyingPartyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryAutoConfiguration.ForwardedHeaderFilterConfiguration:
      Did not match:
         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.application.admin.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder' simpleAsyncTaskExecutorBuilder (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutorVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder' simpleAsyncTaskSchedulerBuilder (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.spring6.SpringTemplateEngine' (OnClassCondition)

   TransactionAutoConfiguration#transactionalOperator:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.AspectJTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.transaction.aspectj.AbstractTransactionAspect; SearchStrategy: all) did not find any beans of type org.springframework.transaction.aspectj.AbstractTransactionAspect (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) found beans of type 'org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration' org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)
         - Ancestor org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   ValidationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.validation.executable.ExecutableValidator' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) did not find property 'enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ProblemDetailsErrorHandlingConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.problemdetails.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarVersionLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSessionIdResolverAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Mono' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.ee10.websocket.jakarta.server.config.JakartaWebSocketServletContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.jdbc.XADataSourceWrapper; SearchStrategy: all) did not find any beans of type org.springframework.boot.jdbc.XADataSourceWrapper (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'jakarta.transaction.TransactionManager', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration

    org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



2025-06-26 17:59:02,343 INFO org.springframework.boot.StartupInfoLogger [Test worker] Started DagParseTest in 14.916 seconds (process running for 18.25)
2025-06-26 17:59:02,412 DEBUG org.springframework.boot.availability.ApplicationAvailabilityBean [Test worker] Application availability state LivenessState changed to CORRECT
2025-06-26 17:59:02,421 DEBUG org.springframework.boot.availability.ApplicationAvailabilityBean [Test worker] Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
2025-06-26 17:59:02,429 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 0, missCount = 1, failureCount = 0]
2025-06-26 17:59:02,431 DEBUG org.springframework.test.context.web.ServletTestExecutionListener [Test worker] Setting up MockHttpServletRequest, MockHttpServletResponse, ServletWebRequest, and RequestContextHolder for test class com.qihoo.feature.DagParseTest
2025-06-26 17:59:02,484 DEBUG org.springframework.test.context.support.DependencyInjectionTestExecutionListener [Test worker] Performing dependency injection for test class com.qihoo.feature.DagParseTest
2025-06-26 17:59:02,489 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 1, missCount = 1, failureCount = 0]
2025-06-26 17:59:02,497 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 2, missCount = 1, failureCount = 0]
2025-06-26 17:59:02,504 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] Before test method: class [DagParseTest], method [parse2Test], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-26 17:59:02,508 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 3, missCount = 1, failureCount = 0]
2025-06-26 17:59:02,509 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 4, missCount = 1, failureCount = 0]
2025-06-26 17:59:04,243 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 5, missCount = 1, failureCount = 0]
2025-06-26 17:59:04,253 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 6, missCount = 1, failureCount = 0]
2025-06-26 17:59:04,272 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:04,747 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:04,834 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:04,978 DEBUG org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler [Test worker] Creating new EntityManager for shared EntityManager invocation
2025-06-26 17:59:05,076 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 7, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,077 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 8, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,081 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 9, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,083 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 10, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,084 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 11, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,085 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 12, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,088 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 13, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,089 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] After test method: class [DagParseTest], method [parse2Test], class annotated with @DirtiesContext [false] with mode [null], method annotated with @DirtiesContext [false] with mode [null]
2025-06-26 17:59:05,089 DEBUG org.springframework.test.context.web.ServletTestExecutionListener [Test worker] Resetting RequestContextHolder for test class com.qihoo.feature.DagParseTest
2025-06-26 17:59:05,105 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 14, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,106 DEBUG org.springframework.test.context.cache.DefaultContextCache [Test worker] Spring test ApplicationContext cache statistics: [DefaultContextCache@5910c67e size = 1, maxSize = 32, parentContextCount = 0, hitCount = 15, missCount = 1, failureCount = 0]
2025-06-26 17:59:05,106 DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener [Test worker] After test class: class [DagParseTest], class annotated with @DirtiesContext [false] with mode [null]
2025-06-26 17:59:05,137 DEBUG org.springframework.context.support.AbstractApplicationContext [SpringApplicationShutdownHook] Closing org.springframework.web.context.support.GenericWebApplicationContext@33ecbd6c, started on Thu Jun 26 17:58:48 CST 2025
2025-06-26 17:59:05,140 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [SpringApplicationShutdownHook] Stopping beans in phase 1073741823
2025-06-26 17:59:05,141 DEBUG org.springframework.context.support.DefaultLifecycleProcessor [SpringApplicationShutdownHook] Bean 'applicationTaskExecutor' completed its stop procedure
2025-06-26 17:59:05,142 DEBUG org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup [SpringApplicationShutdownHook] Stopping beans in phase -2147483647
2025-06-26 17:59:05,142 DEBUG org.springframework.context.support.DefaultLifecycleProcessor [SpringApplicationShutdownHook] Bean 'springBootLoggingLifecycle' completed its stop procedure
2025-06-26 17:59:05,143 DEBUG org.springframework.scheduling.concurrent.ExecutorConfigurationSupport [SpringApplicationShutdownHook] Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-26 17:59:05,145 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'analysisPersistenceUnit'
2025-06-26 17:59:05,148 INFO org.springframework.orm.jpa.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'fepPersistenceUnit'
2025-06-26 17:59:05,149 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-26 17:59:08,200 INFO com.zaxxer.hikari.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
