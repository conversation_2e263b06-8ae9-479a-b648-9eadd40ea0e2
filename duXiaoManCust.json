{"nodes": [{"id": "13e7ebd2-5319-497e-848a-edd2acdae86c", "shape": "custom-rect", "data": {"componentName": "initComponent", "content": {"name": "eduLevel2_new 输入项加工", "featureCategoryKey": "duXiaoManCust", "featureCategoryName": "eduLevel2_new 输入项加工", "processDesc": "eduLevel2_new 输入项加工不限产品,实时计算,审批流程,企查查全部企业信息", "featureCategoryType": "JSONOBJECT", "dimensions": "biz", "param": "request", "status": "enable"}}, "attrs": {"text": {"text": "初始化 组件"}, "image": {"xlink:href": "/img/init.a2f7187f.svg"}, "label": {"text": "duXiaoManCust"}}, "position": {"x": 700.0, "y": 30.0}}, {"id": "62f04c1c-1c11-41a7-81fe-206671e4d89f", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "userSnapshot", "status": "enable", "namespace": "userSnapshot", "componentParams": "", "onError": "T", "serviceBean": "creditCasUserSnapshotDataExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "userSnapshot"}}, "position": {"x": 340.0, "y": 95.0}}, {"id": "714d5cec-bcc4-4839-a0ff-b88c893db457", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "custSnapshot", "status": "enable", "namespace": "custSnapshot", "componentParams": "{\"moduleNo\":\"CAS\"}", "onError": "T", "serviceBean": "creditcasCustSnapshotDataExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "custSnapshot"}}, "position": {"x": 710.0, "y": 95.0}}, {"id": "5e2dcdc4-96b1-406e-8e5b-d2394376bd11", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "apvCreditHistory", "status": "enable", "namespace": "apvCreditHistory", "componentParams": "{\"moduleNo\":\"CAS\",\"intfTypes\":[\"OcrImgZxDuXiaoMan\"]}", "onError": "T", "serviceBean": "casCreditHistoryCustNewDataExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "apvCreditHistory"}}, "position": {"x": 700.0, "y": 160.0}}, {"id": "14b68d53-9f07-4ae7-b250-1bb87fef2986", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "duXiaoMan", "status": "enable", "namespace": "duXiaoMan", "componentParams": "{\"creditType\":\"OcrImgZxDuXiaoMan\"}", "onError": "T", "serviceBean": "creditCommonExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "duXiaoMan"}}, "position": {"x": 700.0, "y": 225.0}}, {"id": "30dc22f7-994a-4541-926e-bbe1344209db", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "duXiaoManProcRealTime", "status": "enable", "namespace": "duXiaoManProcRealTime", "componentParams": "{\"moduleNo\":\"OcrImgZxDuXiaoManRealTimeModule\",\"dataNames\":[{\"dataName\":\"duXiaoMan\"},{\"dataName\":\"custSnapshot\",\"toKey\":\"custSnapshot\"},{\"dataName\":\"userSnapshot\",\"toKey\":\"userSnapshot\"}]}", "onError": "T", "serviceBean": "commonFeaturePlatformV2DataExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "duXiaoManProcRealTime"}}, "position": {"x": 340.0, "y": 290.0}}, {"id": "3d3eac07-feb7-41cb-9c3b-dcaaf051b15a", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "duXiaoManProc", "status": "enable", "namespace": "duXiaoManProc", "componentParams": "{\"creditType\":\"OcrImgZxDuXiaoMan\",\"moduleNo\":\"OcrImgZxDuXiaoMan\"}", "onError": "T", "serviceBean": "commonCreditFeatureDataExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "duXiaoManProc"}}, "position": {"x": 710.0, "y": 290.0}}, {"id": "dbeb2307-6a2a-42b8-934b-ce07746aed24", "shape": "custom-rect", "data": {"componentName": "featureElementComponent", "content": {"name": "duXiaoManProcRealTime", "status": "enable", "featureItems": [{"featureKey": "isSamePhoneNo", "featureDesc": "", "namespace": "DATA.duXiaoManProcRealTime", "valuePath": "", "srcAttr": "isSamePhoneNo", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSameIdNo", "featureDesc": "", "namespace": "DATA.duXiaoManProcRealTime", "valuePath": "", "srcAttr": "isSameIdNo", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSameName", "featureDesc": "", "namespace": "DATA.duXiaoManProcRealTime", "valuePath": "", "srcAttr": "isSameName", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}]}}, "attrs": {"text": {"text": "特征元 组件"}, "image": {"xlink:href": "/img/meta.aead177f.svg"}, "label": {"text": "特征元 :duXiaoManProcRealTime"}}, "position": {"x": 340.0, "y": 355.0}}, {"id": "ff824bde-1700-4e01-86be-2e99455ced94", "shape": "custom-rect", "data": {"componentName": "featureElementComponent", "content": {"name": "duXiaoManProc", "status": "enable", "featureItems": [{"featureKey": "queryState", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "queryState", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "timeQuery", "featureDesc": "", "namespace": "DATA.duXiaoManProc.creditQueryRecord", "valuePath": "", "srcAttr": "timeQuery", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "name", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "name", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "phoneNo", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "phoneNo", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "idNum", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "idNum", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "totalAmount", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "totalAmount", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "interestRate", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "interestRate", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "certChannel", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "certChannel", "formatAction": "", "formatParam": "", "dataType": "String", "onError": "R_E", "onEmpty": "R_W"}, {"featureKey": "isUpdated", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isUpdated", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "earliestCreatedTime", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "earliestCreatedTime", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "latestCreatedTime", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "latestCreatedTime", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "createdTimeSpan", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "createdTimeSpan", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "earliestUploadTime", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "earliestUploadTime", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "latestUploadTime", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "latestUploadTime", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "uploadTimeSpan", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "uploadTimeSpan", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "earliestPhotoTime", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "earliestPhotoTime", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "latestPhotoTime", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "latestPhotoTime", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "photoTimeSpan", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "photoTimeSpan", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSameResolutionRatio", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isSameResolutionRatio", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSameDevice", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isSameDevice", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSameLocation", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isSameLocation", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSameWidth", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isSameWidth", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSameConnectionNode", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isSameConnectionNode", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isExistHistoryLibrary", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isExistHistoryLibrary", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isExistBlackArea", "featureDesc": "", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "isExistBlackArea", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "interestRateWithStrikethrough", "featureDesc": "划线的年化利率", "namespace": "DATA.duXiaoManProc", "valuePath": "", "srcAttr": "interestRateWithStrikethrough", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": "划线的年化利率"}]}}, "attrs": {"text": {"text": "特征元 组件"}, "image": {"xlink:href": "/img/meta.aead177f.svg"}, "label": {"text": "特征元 :duXiaoManProc"}}, "position": {"x": 710.0, "y": 355.0}}], "edges": [{"id": "5f2a69ca-56dc-4043-8839-71a2c2659230", "shape": "custom-edge", "source": "13e7ebd2-5319-497e-848a-edd2acdae86c", "target": "62f04c1c-1c11-41a7-81fe-206671e4d89f", "inherit": "edge"}, {"id": "fc3596e9-d0e7-48a9-9236-9d238187d831", "shape": "custom-edge", "source": "13e7ebd2-5319-497e-848a-edd2acdae86c", "target": "714d5cec-bcc4-4839-a0ff-b88c893db457", "inherit": "edge"}, {"id": "205912b9-b732-4bf5-8872-7ce57787b727", "shape": "custom-edge", "source": "62f04c1c-1c11-41a7-81fe-206671e4d89f", "target": "5e2dcdc4-96b1-406e-8e5b-d2394376bd11", "inherit": "edge"}, {"id": "e618faa5-623a-4889-a739-dd4105179b0d", "shape": "custom-edge", "source": "5e2dcdc4-96b1-406e-8e5b-d2394376bd11", "target": "14b68d53-9f07-4ae7-b250-1bb87fef2986", "inherit": "edge"}, {"id": "f4f2c919-e2bc-40a5-ae57-469942fdc3a3", "shape": "custom-edge", "source": "14b68d53-9f07-4ae7-b250-1bb87fef2986", "target": "30dc22f7-994a-4541-926e-bbe1344209db", "inherit": "edge"}, {"id": "b6c2cf5c-7736-407d-a518-96f1123b1612", "shape": "custom-edge", "source": "5e2dcdc4-96b1-406e-8e5b-d2394376bd11", "target": "3d3eac07-feb7-41cb-9c3b-dcaaf051b15a", "inherit": "edge"}, {"id": "4186763a-92b6-4fd7-907c-7d23f3522753", "shape": "custom-edge", "source": "14b68d53-9f07-4ae7-b250-1bb87fef2986", "target": "3d3eac07-feb7-41cb-9c3b-dcaaf051b15a", "inherit": "edge"}, {"id": "8077663b-67d1-4067-b7de-cd97c7e13075", "shape": "custom-edge", "source": "30dc22f7-994a-4541-926e-bbe1344209db", "target": "dbeb2307-6a2a-42b8-934b-ce07746aed24", "inherit": "edge"}, {"id": "56813d8c-99bc-4876-8c6f-5718685778f9", "shape": "custom-edge", "source": "3d3eac07-feb7-41cb-9c3b-dcaaf051b15a", "target": "ff824bde-1700-4e01-86be-2e99455ced94", "inherit": "edge"}]}