{"nodes": [{"id": "675432ea-2459-4af0-b89d-541036aa4405", "shape": "custom-rect", "data": {"componentName": "initComponent", "content": {"name": "借款扩展信息", "featureCategoryKey": "drawExtInfo", "featureCategoryName": "借款扩展信息", "processDesc": "根据客户号和借款申请号查apv的借款扩展信息借条动支,实时计算,借款信息,借款扩展信息", "featureCategoryType": "JSONOBJECT", "dimensions": "biz", "param": "request", "status": "enable"}}, "attrs": {"text": {"text": "初始化 组件"}, "image": {"xlink:href": "/img/init.a2f7187f.svg"}, "label": {"text": "drawExtInfo"}}, "position": {"x": 700.0, "y": 30.0}}, {"id": "58c7b15a-3b79-43c1-892d-a913f82676d1", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "drawExtInfo", "status": "enable", "namespace": "drawExtInfo", "componentParams": "{}", "onError": "T", "serviceBean": "drawExtInfoDataExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "drawExtInfo"}}, "position": {"x": 700.0, "y": 95.0}}, {"id": "3a5a64f4-a0c1-45db-9b3e-9b402e41f940", "shape": "custom-rect", "data": {"componentName": "featureElementComponent", "content": {"name": "drawExtInfo", "status": "enable", "featureItems": [{"featureKey": "isTemporaryAmtUsed", "featureDesc": "临时额度是否使用", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "isTemporaryAmtUsed", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "isSignAuthorization", "featureDesc": "是否签署学历学籍协议", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "isSignAuthorization", "formatAction": "", "formatParam": "", "dataType": "String", "onError": "R_E", "onEmpty": "R_W", "processLogic": "从applyExt表获取"}, {"featureKey": "firstSelf", "featureDesc": "初审终审标识", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "firstSelf", "formatAction": "", "formatParam": "", "dataType": "String", "onError": "R_E", "onEmpty": "R", "processLogic": "是否自营Y/N"}, {"featureKey": "firstLoanRule", "featureDesc": "新增首贷分发规则是否生效标签", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "firstLoanRule", "formatAction": "", "formatParam": "", "dataType": "String", "onError": "R_E", "onEmpty": "R", "processLogic": "Y：首贷分发规则生效N：首贷分发规则未生效"}, {"featureKey": "loanFuqiangFlag", "featureDesc": "", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "loanFuqiangFlag", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "trialRiskTag", "featureDesc": "", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "trialRiskTag", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "appendTime", "featureDesc": "", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "appendTime", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "signVIP", "featureDesc": "付费会员识别标识", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "signVIP", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": "1:常规借款;2:搭售会员订单;3:会员额度支付（非续费）;4:会员自动续费"}, {"featureKey": "ice36Flag", "featureDesc": "是否智信辅助流程标识 Y/N", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "ice36Flag", "formatAction": "", "formatParam": "", "dataType": "String", "onError": "R_E", "onEmpty": "R", "processLogic": "是否智信辅助流程标识 Y/N"}, {"featureKey": "multiDrawFlow", "featureDesc": "连续借款输入项", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "multiDrawFlow", "formatAction": "", "formatParam": "", "dataType": "String", "onError": "R_W", "onEmpty": "R_W", "processLogic": "multiDrawFlow = 1 连续借款;multiDrawFlow = 2 满额追加借款;multiDrawFlow = 0 常规借款"}, {"featureKey": "drawNegativeFlag", "featureDesc": "交易负向灰度标签", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "drawNegativeFlag", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": "交易负向灰度标签"}, {"featureKey": "isExternalDraw", "featureDesc": "新增是否为端外订单", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "isExternalDraw", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": "从drawExt表获取"}, {"featureKey": "externalSubmitOrderChannel", "featureDesc": "端外交易订单提交渠道", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "externalSubmitOrderChannel", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": "从drawExt表获取"}, {"featureKey": "isNoPasswordTrade", "featureDesc": "新增是否免密支付", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "isNoPasswordTrade", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": "从drawExt表获取"}, {"featureKey": "visibleAmt", "featureDesc": "客户当前可见额度", "namespace": "DATA.drawExtInfo", "valuePath": "", "srcAttr": "visibleAmt", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_W", "onEmpty": "R_W", "processLogic": "从drawExt表获取"}]}}, "attrs": {"text": {"text": "特征元 组件"}, "image": {"xlink:href": "/img/meta.aead177f.svg"}, "label": {"text": "特征元 :drawExtInfo"}}, "position": {"x": 700.0, "y": 160.0}}], "edges": [{"id": "94e2d4e7-06ec-4a5c-b144-9cb4ace176b4", "shape": "custom-edge", "source": "675432ea-2459-4af0-b89d-541036aa4405", "target": "58c7b15a-3b79-43c1-892d-a913f82676d1", "inherit": "edge"}, {"id": "9a6d0acc-bbca-423a-97f2-31837445d226", "shape": "custom-edge", "source": "58c7b15a-3b79-43c1-892d-a913f82676d1", "target": "3a5a64f4-a0c1-45db-9b3e-9b402e41f940", "inherit": "edge"}]}