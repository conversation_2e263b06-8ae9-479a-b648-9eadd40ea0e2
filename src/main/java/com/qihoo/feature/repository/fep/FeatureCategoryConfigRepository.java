package com.qihoo.feature.repository.fep;

import com.qihoo.feature.entity.fep.FeatureCategoryConfigEntity;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface FeatureCategoryConfigRepository extends PagingAndSortingRepository<FeatureCategoryConfigEntity, Long> {


    List<FeatureCategoryConfigEntity> findFeatureCategoryConfigEntitiesByFeatureCategoryKeyIn(List<String> featureCategoryKeys);


    FeatureCategoryConfigEntity findByFeatureCategoryKey(String featureCategoryKey);
}
