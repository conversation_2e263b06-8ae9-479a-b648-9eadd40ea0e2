package com.qihoo.feature.repository.fep;

import com.qihoo.feature.entity.fep.FeatureCategoryInfoEntity;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface FeatureCategoryInfoRepository extends CrudRepository<FeatureCategoryInfoEntity, Long> {


    List<FeatureCategoryInfoEntity> findFeatureCategoryInfoEntitiesByFeatureCategoryKeyIn(List<String> featureCategoryKeys);


    FeatureCategoryInfoEntity findByFeatureCategoryKey(String featureCategoryKey);

}
