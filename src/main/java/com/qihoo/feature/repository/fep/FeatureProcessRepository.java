package com.qihoo.feature.repository.fep;

import com.qihoo.feature.entity.fep.FeatureProcessEntity;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface FeatureProcessRepository extends CrudRepository<FeatureProcessEntity, Long> {


    List<FeatureProcessEntity> findFeatureProcessEntitiesByFeatureCategoryKeyInAndStatusOrderByFeatureCategoryKeyAscRunOrderAsc(List<String> featureCategoryKeys, String status);


    List<FeatureProcessEntity> findByFeatureCategoryKeyAndStatus(String featureCategoryKey, String status);
}
