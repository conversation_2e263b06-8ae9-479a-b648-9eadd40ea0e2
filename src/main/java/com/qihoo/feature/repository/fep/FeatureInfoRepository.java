package com.qihoo.feature.repository.fep;

import com.qihoo.feature.entity.fep.FeatureInfoEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface FeatureInfoRepository extends CrudRepository<FeatureInfoEntity, Long> {


    List<FeatureInfoEntity> findByFeatureGroup(String featureGroup);


    @Query(nativeQuery = true, value = """
            SELECT  distinct SUBSTRING_INDEX(replace(src_path,'DATA.',''), '.',1) AS dataName
            FROM
                feature_info  where feature_group = :featureGroup
            """)
    Set<String> queryByFeatureGroup(@Param("featureGroup") String featureGroup);
}
