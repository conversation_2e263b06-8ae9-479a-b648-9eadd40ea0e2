package com.qihoo.feature.dto;


import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FeatureItem {

    private String featureKey;
    private String featureDesc;
    private String namespace;
    private String valuePath;
    private String srcAttr;
    private String formatAction;
    private String formatParam;
    private String dataType;
    private String categoryCodes;
    private String categoryNames;
    private String onError;
    private String onEmpty;
    private String processLogic;


}
