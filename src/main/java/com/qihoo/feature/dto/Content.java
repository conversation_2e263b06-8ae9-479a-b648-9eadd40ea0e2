package com.qihoo.feature.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Builder
@Getter
@Setter
public class Content {
    private String name;
    private String featureCategoryKey;
    private String featureCategoryName;
    private String processDesc;
    private String featureCategoryType;
    private String dimensions;
    private String param;
    private String status;
    private String className;
    private String namespace;
    private String componentParams;
    private String onError;
    private List<FeatureItem> featureItems;
    private String serviceBean;
    private String processorType;


}
