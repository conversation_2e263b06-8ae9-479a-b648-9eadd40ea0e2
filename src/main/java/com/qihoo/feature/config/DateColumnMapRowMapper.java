package com.qihoo.feature.config;

import org.springframework.jdbc.core.ColumnMapRowMapper;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;


@Component
public class DateColumnMapRowMapper extends ColumnMapRowMapper {
    @Override
    protected Object getColumnValue(ResultSet rs, int index) throws SQLException {
        Object value = super.getColumnValue(rs, index);

        if (value instanceof java.sql.Date || value instanceof java.sql.Time || value instanceof java.sql.Timestamp) {
            return new java.util.Date(((java.util.Date) value).getTime()); // 统一转换
        }

        return value;
    }
}