package com.qihoo.feature.config;


import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "entityManagerFactoryLocal",
        transactionManagerRef = "transactionManageLocal",
        basePackages = {"com.qihoo.feature.repository.local"})
public class LocalDataSourceConfig {

    @Resource(name = "localDataSource")
    private DataSource analysisDataSource;

    @Resource
    private JpaProperties jpaProperties;
    @Resource
    private HibernateProperties hibernateProperties;

    private Map<String, Object> getVendorProperties() {
        return hibernateProperties.determineHibernateProperties(jpaProperties.getProperties(), new HibernateSettings());
    }

    @Bean(name = "entityManagerLocal")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return entityManagerFactoryAnalysis(builder).getObject().createEntityManager();
    }

    @Bean(name = "entityManagerFactoryLocal")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryAnalysis(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(analysisDataSource)
                .packages("com.qihoo.feature.entity.local")
                .persistenceUnit("analysisPersistenceUnit")
                .properties(getVendorProperties())
                .build();
    }

    @Bean(name = "transactionManageLocal")
    public PlatformTransactionManager transactionManageAnalysis(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(entityManagerFactoryAnalysis(builder).getObject());
    }


}
