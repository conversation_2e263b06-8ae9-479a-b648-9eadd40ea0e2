package com.qihoo.feature;

import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.layout.AbstractLayout;
import org.apache.logging.log4j.core.layout.AbstractStringLayout;

import java.nio.charset.Charset;

public class MyJ<PERSON>NLayOut  extends AbstractStringLayout {
    protected MyJSONLayOut(Configuration config, Charset aCharset, Serializer headerSerializer, Serializer footerSerializer) {
        super(config, aCharset, headerSerializer, footerSerializer);
    }


/*
    @Override
    public byte[] toByteArray(LogEvent event) {
        return new byte[0];
    }
*/

    @Override
    public String toSerializable(LogEvent event) {
        return "";
    }

    @Override
    public String getContentType() {
        return "";
    }
}
