package com.qihoo.feature.entity;

import lombok.Data;

import java.util.Date;

/**
 * 脚本特征大类ES冗余
 */
@Data
public class FeatureCategorySummaryEntity {

    /**
     * 特征大类
     */
    private String featureCategoryKey;

    /**
     * 特征大类名称
     */
    private String featureCategoryName;


    /**
     * 离线表名称
     */
    private String offlineTableName;


    /**
     * 加工类型
     */
    private String processType;


    /**
     * 是否已经上线
     */
    private Boolean isOnline;

    /**
     * 是否有暂存
     */

    private Boolean isTempStaging;


    /**
     * 暂存操作用户
     */
    private String tempStagingUser;


    /**
     * 加工维度
     */
    private String dimensions;

    /**
     * 特征分类名称
     */


    /**
     * 加工描述
     */
    private String processDesc;

    /**
     * tags
     */
    private String tags;

    /**
     * 入参
     */
    private String param;


    private String status;

    private String version;

    private String shadowVersion;


    private Date dateCreated;

    private String createdBy;

    private Date dateUpdated;

    private String updatedBy;


    /**
     * JSONOBJECT, JSONARRAY
     */
    private String featureCategoryType;


    private String featureGroup;



}
