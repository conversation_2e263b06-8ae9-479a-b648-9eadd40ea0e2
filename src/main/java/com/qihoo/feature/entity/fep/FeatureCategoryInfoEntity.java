package com.qihoo.feature.entity.fep;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
@Table(name = "feature_category_info")
@Entity
public class FeatureCategoryInfoEntity  {

    /**
     * 特征大类Key,用于区分维度的唯一ID
     */
    @Column(name = "feature_category_key")
    private String featureCategoryKey;
    /**
     * 特征大类名称,特征大类描述
     */
    @Column(name = "feature_category_name")
    private String featureCategoryName;
    /**
     * 归属产品,目前取值：MMP(多产品混合加工)、360JIETIAO、360BIG、360MALL、360JINXIAO、360SME、360YINGJI
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 产生阶段,101、301、501、601、701、B101、B301、B501、B601、B701等，每个产品对应的一个基准阶段。
     */
    @Column(name = "generation_stage")
    private String generationStage;

    /**
     * 需求标题,对应需求描述。方便后续历史追踪
     */
    @Column(name = "need_title")
    private String needTitle;
    /**
     * 需求URL,对应需求URL
     */
    @Column(name = "need_url")
    private String needUrl;
    /**
     * 需求提出人
     */
    @Column(name = "proposer_by")
    private String proposerBy;

    /**
     * 上线时间
     */
    @Column(name = "date_release")
    private Date dateRelease;

    /**
     * 数据加工逻辑描述
     */
    @Column(name = "process_desc")
    private String processDesc;

    /**
     * 数据标签
     */
    @Column(name = "tags")
    private String tags;

    public FeatureCategoryInfoEntity() {
    }


    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 创建时间
     */
    @Column(name = "date_created")
    public Date dateCreated;

    /**
     * 创建时间
     */
    @Column(name = "created_by")
    public String createdBy;

    /**
     * 修改时间
     */
    @Column(name = "date_updated")
    public Date dateUpdated;

    /**
     * 创建时间
     */
    @Column(name = "updated_by")
    public String updatedBy;
}
