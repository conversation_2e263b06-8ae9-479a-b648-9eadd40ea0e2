package com.qihoo.feature.entity.fep;

import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/11/20.
 */
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Data
@Table(name = "feature_info")
@Entity
public class FeatureInfoEntity {

    @Builder(toBuilder = true)
    public FeatureInfoEntity(Long id, Date dateCreated, String createdBy, Date dateUpdated, String updatedBy, String featureKey, String featureName, String featureGroup, String processLogic, String dataType, String defaultValue, String sourceData, String valueRange, Date submitTime, String status, String onError, String onEmpty, String formatAction, String formatParam, String srcPath, String srcAttr, boolean pathSrcAttr) {
        this.id = id;
        this.dateCreated = dateCreated;
        this.createdBy = createdBy;
        this.dateUpdated = dateUpdated;
        this.updatedBy = updatedBy;
        this.featureKey = featureKey;
        this.featureName = featureName;
        this.featureGroup = featureGroup;
        this.processLogic = processLogic;
        this.dataType = dataType;
        this.defaultValue = defaultValue;
        this.sourceData = sourceData;
        this.valueRange = valueRange;
        this.submitTime = submitTime;
        this.status = status;
        this.onError = onError;
        this.onEmpty = onEmpty;
        this.formatAction = formatAction;
        this.formatParam = formatParam;
        this.srcPath = srcPath;
        this.srcAttr = srcAttr;
        this.pathSrcAttr = pathSrcAttr;
    }

    /**
     * 特征数据Key,在同一个特征大类下必须唯一，不同大类特征下建议唯一
     */
    @Column(name = "feature_key")
    private String featureKey;

    /**
     * 特征名称,简短的描述
     */
    @Column(name = "feature_name")
    private String featureName;

    /**
     * 特征数据组,用于区分维度的唯一ID
     */
    @Column(name = "feature_group")
    private String featureGroup;

    /**
     * 特征加工逻辑描述
     */
    @Column(name = "process_logic")
    private String processLogic;

    /**
     * 数据类型	String、Interge、Double、Date、Long、Float
     */
    @Column(name = "data_type")
    private String dataType;

    /**
     * '默认值'
     */
    @Column(name = "default_value")
    private String defaultValue;

    /**
     * 数据来源	所使用数据简述	加工的主要数据来源
     */
    @Column(name = "source_data")
    private String sourceData;

    /**
     * 取值范围
     */
    @Column(name = "value_range")
    private String valueRange;

    /**
     * 加入日期
     */
    @Column(name = "submit_time")
    private Date submitTime;

    /**
     * 状态	1：已上线，2：使用中，3：已下线
     */
    @Column(name = "status")
    private String status;

    /**
     * 错误处理,当出现错误时处理方式	R：继续，R_E：继续但打印ERR，R_W：继续但打印WARN，T：抛异常
     */
    @Column(name = "on_error")
    private String onError;

    /**
     * 空处理,当为空时处理方式	R：继续，R_E：继续但打印ERR，R_W：继续但打印WARN，T：抛异常
     */
    @Column(name = "on_empty")
    private String onEmpty;

    /**
     * 格式化action
     */
    @Column(name = "format_action")
    private String formatAction;

    /**
     * 格式化format参数
     */
    @Column(name = "format_param")
    private String formatParam;

    /**
     * 原路径
     */
    @Column(name = "src_path")
    private String srcPath;

    /**
     * 原属性
     */
    @Column(name = "src_attr")
    private String srcAttr;


    @Transient
    private boolean pathSrcAttr;


    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;
    /**
     * 创建时间
     */
    @Column(name = "date_created")
    public Date dateCreated;

    /**
     * 创建时间
     */
    @Column(name = "created_by")
    public String createdBy;

    /**
     * 修改时间
     */
    @Column(name = "date_updated")
    public Date dateUpdated;

    /**
     * 创建时间
     */
    @Column(name = "updated_by")
    public String updatedBy;
}
