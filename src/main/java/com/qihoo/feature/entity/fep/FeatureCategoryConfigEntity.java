package com.qihoo.feature.entity.fep;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
@Table(name = "feature_category_config")
@Entity
public class FeatureCategoryConfigEntity  {
    /**
     * 特征大类Key,用于区分维度的唯一ID
     */
    @Column(name = "feature_category_key")
    private String featureCategoryKey;

    @Column(name = "feature_group")
    private String featureGroup;

    @Column(name = "pre_processor")
    private String preProcessor;

    /**
     * 特征大类类型	JsonObject、JsonArray
     */
    @Column(name = "feature_category_type")
    private String featureCategoryType;

    @Column(name = "required_params")
    private String requiredParams;

    /**
     * 复用范围,取值范围：复用范围从大到小COMMON：通用复用（跨用户体系可复用，即不会因不同用户体系和产品而变化的数据）USER：用户维度（在同一用户体系内可复用的数据，跨用户体系将不适用）DEVICE：设备维度PRODUCT：产品维度BIZ：业务申请维度REALTIME：实时计算
     */
    @Column(name = "reuse_scope")
    private String reuseScope;
    /**
     * 复用时长,0-100，999：复用当年、月、天、小时,特征有效时长，针对不同的特征类型，数据复用的时长会有差异，需要区分处理。
     */
    @Column(name = "reuse_times")
    private Integer reuseTimes;
    /**
     * 复用时长单位,年Y、月M、天D、小时H、分钟MIN
     */
    @Column(name = "reuse_unit")
    private String reuseUnit;
    /**
     * 缓存时长	0-10000 ，单位：分钟，最长缓存1周时间。	特征数据在缓存中存放时长，为了加快查询时的效率，热点数据应缓存到Redis，但是不宜过长，最长缓存1周时间
     */
    @Column(name = "cache_times")
    private Long cacheTimes;

    /**
     * 状态,INIT：初始化，ONLINE：生效，OFFLINE：下线，DELLE：删除，默认INIT	该状态只有为 ONLINE状态的才进行计算
     */
    @Column(name = "status")
    private String status;
    /**
     * 触发计算事件Key值,特征可以通过事件触发计算，不同的特征可以使用相同的事件来触发，具体事件可通过统一方式设定
     */
    @Column(name = "trigger_event_key")
    private String triggerEventKey;

    /**
     * 大类特征计算结果读写标识，R_W：查写，R_NW：只查不写，NR_W：只写不查，NR_NW：不查不写
     */
    @Column(name = "result_rw_flag")
    private String resultRwFlag;

    public FeatureCategoryConfigEntity() {
    }

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 创建时间
     */
    @Column(name = "date_created")
    public Date dateCreated;

    /**
     * 创建时间
     */
    @Column(name = "created_by")
    public String createdBy;

    /**
     * 修改时间
     */
    @Column(name = "date_updated")
    public Date dateUpdated;

    /**
     * 创建时间
     */
    @Column(name = "updated_by")
    public String updatedBy;
}
