package com.qihoo.feature.entity.fep;


import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Table(name = "feature_process")
@Entity
@Data
public class FeatureProcessEntity  {

    /**
     * 特征数据组,用于区分维度的唯一ID
     */
    @Column(name = "feature_Category_key")
    private String featureCategoryKey;

    @Column(name = "processor_type")
    private String processorType;

    @Column(name = "clean_type")
    private String cleanType;
    /**
     * processor服务类,对应的服务类Bean名称
     */
    @Column(name = "service_bean")
    private String serviceBean;

    /**
     * 状态	enable：有效，unable：无效
     */
    @Column(name = "status")
    private String status;

    /**
     * 错误处理,当出现错误时处理方式	R：继续，R_E：继续但打印ERR，R_W：继续但打印WARN，T：抛异常
     */
    @Column(name = "on_error")
    private String onError;

    /**
     * 如果cleanType为默认则：params配置格式如下
     * [{"src_path":"","dest_path":""}]
     */
    @Column(name = "clean_params")
    private String cleanParams;

    /**
     * 参数
     */
    @Column(name = "call_params")
    private String callParams;

    /**
     * 执行顺序
     */
    @Column(name = "run_order")
    private String runOrder;
    /**
     * 对应的结果key
     */
    @Column(name = "data_name")
    private String dataName;

    /**
     * 依赖的dataname,多个以英文逗号分隔
     */
    @Column(name = "dependencies")
    private String dependencies;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 创建时间
     */
    @Column(name = "date_created")
    public Date dateCreated;

    /**
     * 创建时间
     */
    @Column(name = "created_by")
    public String createdBy;

    /**
     * 修改时间
     */
    @Column(name = "date_updated")
    public Date dateUpdated;

    /**
     * 创建时间
     */
    @Column(name = "updated_by")
    public String updatedBy;
}
