package com.qihoo.feature.entity.local;

import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "fcm_feature_process_definition")
@Entity
public class FeatureProcessDefinitionEntity implements Serializable {
    private static final long serialVersionUID = 131358891071262935L;

    /**
     * 大类名称
     */
    @Column(name = "feature_category_key")
    private String featureCategoryKey;
    /**
     * 加工维度 biz/user/cust
     */
    @Column(name = "dimensions")

    private String dimensions;
    /**
     * 流程定义json
     */
    @Column(name = "definition_json")

    private String definitionJson;
    /**
     * 版本号
     */
    @Column(name = "version")

    private String version;
    /**
     * 状态 enable/disable
     */
    @Column(name = "status")

    private String status;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 创建时间
     */
    @Column(name = "date_created")
    public Date dateCreated;

    /**
     * 创建时间
     */
    @Column(name = "created_by")
    public String createdBy;

    /**
     * 修改时间
     */
    @Column(name = "date_updated")
    public Date dateUpdated;

    /**
     * 创建时间
     */
    @Column(name = "updated_by")
    public String updateBy;


}
