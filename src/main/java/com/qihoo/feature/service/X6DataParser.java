package com.qihoo.feature.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qihoo.feature.dto.*;
import com.qihoo.feature.entity.fep.FeatureCategoryConfigEntity;
import com.qihoo.feature.entity.fep.FeatureCategoryInfoEntity;
import com.qihoo.feature.entity.fep.FeatureInfoEntity;
import com.qihoo.feature.entity.fep.FeatureProcessEntity;
import com.qihoo.feature.repository.fep.FeatureCategoryConfigRepository;
import com.qihoo.feature.repository.fep.FeatureCategoryInfoRepository;
import com.qihoo.feature.repository.fep.FeatureInfoRepository;
import com.qihoo.feature.repository.fep.FeatureProcessRepository;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * X6 数据解析器，用于将特征配置数据转换为 X6 组件数据
 */
@Service
public class X6DataParser {

    // 常量定义
    private static final String INIT_LEVEL = "-9999";
    private static final String FEATURE_ELEMENT_LEVEL = "999999";
    private static final String ENABLE_STATUS = "enable";
    private static final String DATA_PREFIX = "DATA.";
    private static final String DEPENDENCY_SEPARATOR = ",";

    // 组件类型常量
    private static final String JAVA_COMPONENT = "javaComponent";
    private static final String INIT_COMPONENT = "initComponent";
    private static final String FEATURE_ELEMENT_COMPONENT = "featureElementComponent";

    // 形状常量
    private static final String CUSTOM_RECT_SHAPE = "custom-rect";
    private static final String CUSTOM_EDGE_SHAPE = "custom-edge";
    private static final String EDGE_INHERIT = "edge";

    // 布局常量
    private static final int BASE_X_POSITION = 520;
    private static final int BASE_Y_POSITION = 30;
    private static final int Y_STEP = 65;
    private static final int X_STEP_EVEN = 180;
    private static final int X_STEP_ODD = 190;
    private static final int NODES_PER_ROW = 6;

    // 维度常量
    private static final String DIMENSION_BIZ = "biz";
    private static final String DIMENSION_CUST = "cust";
    private static final String DIMENSION_USER = "user";
    private static final String REUSE_SCOPE_CUST = "cust";
    private static final String REUSE_SCOPE_USER = "user";

    // 图片路径常量
    private static final String JAVA_ICON_PATH = "/img/java.c67ece64.svg";
    private static final String INIT_ICON_PATH = "/img/init.a2f7187f.svg";
    private static final String META_ICON_PATH = "/img/meta.aead177f.svg";

    // 文本常量
    private static final String JAVA_COMPONENT_TEXT = "JAVA 组件";
    private static final String INIT_COMPONENT_TEXT = "初始化 组件";
    private static final String FEATURE_ELEMENT_TEXT = "特征元 组件";
    private static final String FEATURE_ELEMENT_LABEL_PREFIX = "特征元 :";
    private static final String REQUEST_PARAM = "request";


    @Resource
    private FeatureProcessRepository featureProcessRepository;
    @Resource
    private FeatureInfoRepository featureInfoRepository;
    @Resource
    private FeatureCategoryConfigRepository featureCategoryConfigRepository;
    @Resource
    private FeatureCategoryInfoRepository featureCategoryInfoRepository;


    /**
     * 将特征分类配置转换为 X6 组件数据
     *
     * @param featureCategoryKey 特征分类键
     * @return X6 组件数据
     */
    public X6ComponentData convert2X6ComponentData(String featureCategoryKey) {
        // 初始化数据结构
        Map<String, List<Node>> levels = new TreeMap<>();
        Map<String, Pair<String, String>> namespaceNodeIdMap = new HashMap<>();
        Map<String, List<String>> layerDataNameMap = new HashMap<>();
        List<Edge> edges = new ArrayList<>();

        // 获取配置信息
        FeatureCategoryConfigEntity featureCategoryConfig =
            featureCategoryConfigRepository.findByFeatureCategoryKey(featureCategoryKey);
        FeatureCategoryInfoEntity featureCategoryInfo =
            featureCategoryInfoRepository.findByFeatureCategoryKey(featureCategoryKey);

        // 生成初始节点
        Node initNode = generateInitNode(featureCategoryConfig, featureCategoryInfo);
        levels.put(INIT_LEVEL, Lists.newArrayList(initNode));

        // 生成特征处理节点
        generateFeatureProcessNodes(featureCategoryKey, levels, layerDataNameMap,
            namespaceNodeIdMap, initNode, edges);

        // 生成特征元素节点
        generateFeatureElementsNode(featureCategoryConfig, namespaceNodeIdMap, edges, levels);

        // 自动布局节点
        List<Node> x6Nodes = autoLayoutNodes(levels);

        // 构建返回结果
        X6ComponentData x6Data = new X6ComponentData();
        x6Data.setEdges(edges);
        x6Data.setNodes(x6Nodes);
        return x6Data;
    }

    /**
     * 生成特征处理节点
     */
    private void generateFeatureProcessNodes(String featureCategoryKey, Map<String, List<Node>> levels,
            Map<String, List<String>> layerDataNameMap, Map<String, Pair<String, String>> namespaceNodeIdMap,
            Node initNode, List<Edge> edges) {

        List<FeatureProcessEntity> enabledFeatureProcess = getEnabledFeatureProcesses(featureCategoryKey);
        Map<String, List<FeatureProcessEntity>> groupByRunOrderMap = groupFeatureProcessesByRunOrder(enabledFeatureProcess);

        String preIndex = null;
        TreeSet<String> orderIndex = new TreeSet<>(groupByRunOrderMap.keySet());

        while (!orderIndex.isEmpty()) {
            String currIndex = orderIndex.pollFirst();
            List<FeatureProcessEntity> featureProcessEntities = groupByRunOrderMap.get(currIndex);

            initializeLevelData(levels, layerDataNameMap, currIndex);

            for (FeatureProcessEntity process : featureProcessEntities) {
                Node node = createFeatureProcessNode(process);
                List<String> dependencies = parseDependencies(process.getDependencies());

                updateMappings(namespaceNodeIdMap, layerDataNameMap, process, node, currIndex);

                createEdgesForNode(node, dependencies, preIndex, initNode, namespaceNodeIdMap,
                    layerDataNameMap, edges);
            }

            preIndex = currIndex;
        }
    }

    /**
     * 获取启用的特征处理实体列表
     */
    private List<FeatureProcessEntity> getEnabledFeatureProcesses(String featureCategoryKey) {
        List<FeatureProcessEntity> enabledFeatureProcess = featureProcessRepository
                .findByFeatureCategoryKeyAndStatus(featureCategoryKey, ENABLE_STATUS);
        enabledFeatureProcess.sort(Comparator.comparing(FeatureProcessEntity::getRunOrder));
        return enabledFeatureProcess;
    }

    /**
     * 按运行顺序分组特征处理实体
     */
    private Map<String, List<FeatureProcessEntity>> groupFeatureProcessesByRunOrder(
            List<FeatureProcessEntity> featureProcesses) {
        return featureProcesses.stream()
                .collect(Collectors.groupingBy(FeatureProcessEntity::getRunOrder));
    }

    /**
     * 初始化层级数据
     */
    private void initializeLevelData(Map<String, List<Node>> levels,
            Map<String, List<String>> layerDataNameMap, String currIndex) {
        levels.putIfAbsent(currIndex, new ArrayList<>());
        layerDataNameMap.putIfAbsent(currIndex, new ArrayList<>());
    }

    /**
     * 创建特征处理节点
     */
    private Node createFeatureProcessNode(FeatureProcessEntity process) {
        Node node = new Node();
        node.setId(UUID.randomUUID().toString());
        node.setShape(CUSTOM_RECT_SHAPE);

        Content content = createFeatureProcessContent(process);
        Data data = Data.builder()
                .componentName(JAVA_COMPONENT)
                .content(content)
                .build();

        Attributes attributes = createFeatureProcessAttributes(process);

        node.setData(data);
        node.setAttrs(attributes);

        return node;
    }

    /**
     * 创建特征处理内容
     */
    private Content createFeatureProcessContent(FeatureProcessEntity process) {
        return Content.builder()
                .name(process.getDataName())
                .serviceBean(process.getServiceBean())
                .namespace(process.getDataName())
                .onError(process.getOnError())
                .processorType(process.getProcessorType())
                .status(process.getStatus())
                .componentParams(process.getCallParams())
                .build();
    }

    /**
     * 创建特征处理属性
     */
    private Attributes createFeatureProcessAttributes(FeatureProcessEntity process) {
        return Attributes.builder()
                .label(Label.builder().text(process.getDataName()).build())
                .image(Image.builder().xlinkHref(JAVA_ICON_PATH).build())
                .text(Text.builder().text(JAVA_COMPONENT_TEXT).build())
                .build();
    }

    /**
     * 解析依赖关系
     */
    private List<String> parseDependencies(String dependencies) {
        return StringUtils.isNotBlank(dependencies) ?
                Arrays.asList(dependencies.split(DEPENDENCY_SEPARATOR)) : new ArrayList<>();
    }

    /**
     * 更新映射关系
     */
    private void updateMappings(Map<String, Pair<String, String>> namespaceNodeIdMap,
            Map<String, List<String>> layerDataNameMap, FeatureProcessEntity process,
            Node node, String currIndex) {
        namespaceNodeIdMap.put(process.getDataName(), Pair.of(node.getId(), currIndex));
        layerDataNameMap.get(currIndex).add(process.getDataName());
    }

    /**
     * 为节点创建边
     */
    private void createEdgesForNode(Node node, List<String> dependencies, String preIndex,
            Node initNode, Map<String, Pair<String, String>> namespaceNodeIdMap,
            Map<String, List<String>> layerDataNameMap, List<Edge> edges) {

        if (preIndex == null) {
            // 第一层直接连接到初始节点
            edges.add(createEdge(initNode.getId(), node.getId()));
        } else {
            // 处理依赖关系
            handleDependencies(node, dependencies, preIndex, namespaceNodeIdMap,
                layerDataNameMap, edges);
        }
    }

    /**
     * 处理依赖关系
     */
    private void handleDependencies(Node node, List<String> dependencies, String preIndex,
            Map<String, Pair<String, String>> namespaceNodeIdMap,
            Map<String, List<String>> layerDataNameMap, List<Edge> edges) {

        List<String> preLayerNamespaces = layerDataNameMap.get(preIndex);
        boolean hasPreLayerDeps = !CollectionUtils.isEmpty(dependencies) &&
                !Sets.intersection(Sets.newHashSet(preLayerNamespaces), Sets.newHashSet(dependencies)).isEmpty();

        // 创建依赖边
        for (String depNamespace : dependencies) {
            Pair<String, String> depNodeInfo = namespaceNodeIdMap.get(depNamespace);
            if (depNodeInfo != null) {
                edges.add(createEdge(depNodeInfo.getFirst(), node.getId()));
            }
        }

        // 如果没有前层依赖，强制添加一个连接
        if (!hasPreLayerDeps && !CollectionUtils.isEmpty(preLayerNamespaces)) {
            String firstNamespace = preLayerNamespaces.get(0);
            Pair<String, String> firstNodeInfo = namespaceNodeIdMap.get(firstNamespace);
            if (firstNodeInfo != null) {
                edges.add(createEdge(firstNodeInfo.getFirst(), node.getId()));
            }
        }
    }


    /**
     * 自动布局节点位置
     *
     * @param levels 按层级分组的节点
     * @return 布局后的节点列表
     */
    private List<Node> autoLayoutNodes(Map<String, List<Node>> levels) {
        List<Node> x6Nodes = new ArrayList<>();
        int yPosition = BASE_Y_POSITION;

        // 遍历每一层并布局（从上到下排列）
        for (String level : levels.keySet()) {
            List<Node> nodesAtLevel = levels.get(level);
            yPosition = layoutNodesInLevel(nodesAtLevel, yPosition, x6Nodes);
        }

        return x6Nodes;
    }

    /**
     * 布局单层节点
     */
    private int layoutNodesInLevel(List<Node> nodesAtLevel, int startYPosition, List<Node> x6Nodes) {
        int yPosition = startYPosition;
        List<List<Node>> partitions = Lists.partition(nodesAtLevel, NODES_PER_ROW);

        for (List<Node> nodes : partitions) {
            layoutNodesInRow(nodes, yPosition);
            x6Nodes.addAll(nodes);
            yPosition += Y_STEP;
        }

        return yPosition;
    }

    /**
     * 布局单行节点
     */
    private void layoutNodesInRow(List<Node> nodes, int yPosition) {
        int baseXLeft = BASE_X_POSITION;
        int baseXRight = BASE_X_POSITION;

        for (int i = 0; i < nodes.size(); i++) {
            Node node = nodes.get(i);
            Position position = calculateNodePosition(i, nodes.size(), baseXLeft, baseXRight, yPosition);
            node.setPosition(position);
        }
    }

    /**
     * 计算节点位置
     */
    private Position calculateNodePosition(int index, int totalNodes, int baseXLeft, int baseXRight, int yPosition) {
        int step = index % 2 == 0 ? X_STEP_EVEN : X_STEP_ODD;
        int xPosition;

        if (index < totalNodes / 2) {
            xPosition = baseXLeft - step * (index + 1);
        } else {
            xPosition = baseXRight + step * (index + 1 - totalNodes / 2);
        }

        return Position.builder().x(xPosition).y(yPosition).build();
    }


    /**
     * 生成特征元素节点
     *
     * @param featureCategoryConfig 特征分类配置
     * @param namespaceNodeIdMap 命名空间到节点ID的映射
     * @param edges 边列表
     * @param levels 层级节点映射
     */
    private void generateFeatureElementsNode(FeatureCategoryConfigEntity featureCategoryConfig,
            Map<String, Pair<String, String>> namespaceNodeIdMap, List<Edge> edges,
            Map<String, List<Node>> levels) {

        List<FeatureInfoEntity> featureInfos = featureInfoRepository
                .findByFeatureGroup(featureCategoryConfig.getFeatureGroup());

        Map<String, List<FeatureInfoEntity>> srcPathMap = groupFeatureInfosBySrcPath(featureInfos);
        List<Node> featureElementNodes = createFeatureElementNodes(srcPathMap, namespaceNodeIdMap, edges);

        levels.put(FEATURE_ELEMENT_LEVEL, featureElementNodes);
    }

    /**
     * 按源路径分组特征信息
     */
    private Map<String, List<FeatureInfoEntity>> groupFeatureInfosBySrcPath(List<FeatureInfoEntity> featureInfos) {
        return featureInfos.stream()
                .collect(Collectors.groupingBy(entity -> processSrcPath(entity.getSrcPath())));
    }

    /**
     * 创建特征元素节点列表
     */
    private List<Node> createFeatureElementNodes(Map<String, List<FeatureInfoEntity>> srcPathMap,
            Map<String, Pair<String, String>> namespaceNodeIdMap, List<Edge> edges) {

        List<Node> featureElementNodes = new ArrayList<>();

        srcPathMap.forEach((srcPath, featureInfoList) -> {
            List<FeatureItem> featureItems = createFeatureItems(featureInfoList);
            Node node = createFeatureElementNode(srcPath, featureItems);

            // 只有找到对应的命名空间节点才添加
            Pair<String, String> nodeIdRunOrderPair = namespaceNodeIdMap.get(srcPath);
            if (Objects.nonNull(nodeIdRunOrderPair)) {
                edges.add(createEdge(nodeIdRunOrderPair.getFirst(), node.getId()));
                featureElementNodes.add(node);
            }
        });

        return featureElementNodes;
    }

    /**
     * 创建特征项列表
     */
    private List<FeatureItem> createFeatureItems(List<FeatureInfoEntity> featureInfoList) {
        return featureInfoList.stream()
                .map(this::createFeatureItem)
                .collect(Collectors.toList());
    }

    /**
     * 创建单个特征项
     */
    private FeatureItem createFeatureItem(FeatureInfoEntity featureInfo) {
        return FeatureItem.builder()
                .featureKey(featureInfo.getFeatureKey())
                .dataType(featureInfo.getDataType())
                .featureDesc(featureInfo.getFeatureName())
                .formatAction(featureInfo.getFormatAction())
                .namespace(featureInfo.getSrcPath())
                .srcAttr(featureInfo.getSrcAttr())
                .valuePath("")
                .formatParam(featureInfo.getFormatParam())
                .onError(featureInfo.getOnError())
                .onEmpty(featureInfo.getOnEmpty())
                .processLogic(featureInfo.getProcessLogic())
                .build();
    }

    /**
     * 创建特征元素节点
     */
    private Node createFeatureElementNode(String srcPath, List<FeatureItem> featureItems) {
        Node node = new Node();
        node.setId(UUID.randomUUID().toString());
        node.setShape(CUSTOM_RECT_SHAPE);

        Content content = Content.builder()
                .name(srcPath)
                .status(ENABLE_STATUS)
                .featureItems(featureItems)
                .build();

        Data data = Data.builder()
                .componentName(FEATURE_ELEMENT_COMPONENT)
                .content(content)
                .build();

        Attributes attributes = Attributes.builder()
                .label(Label.builder().text(FEATURE_ELEMENT_LABEL_PREFIX + srcPath).build())
                .image(Image.builder().xlinkHref(META_ICON_PATH).build())
                .text(Text.builder().text(FEATURE_ELEMENT_TEXT).build())
                .build();

        node.setData(data);
        node.setAttrs(attributes);

        return node;
    }

    /**
     * 处理源路径，提取命名空间
     */
    private static String processSrcPath(String srcPath) {
        if (srcPath.startsWith(DATA_PREFIX)) {
            srcPath = srcPath.substring(DATA_PREFIX.length());
        }
        return srcPath.split("\\.")[0]; // 按 "." 分隔，取第一个元素
    }

    /**
     * 生成初始化节点
     *
     * @param featureCategoryConfig 特征分类配置
     * @param featureCategoryInfo 特征分类信息
     * @return 初始化节点
     */
    private Node generateInitNode(FeatureCategoryConfigEntity featureCategoryConfig,
            FeatureCategoryInfoEntity featureCategoryInfo) {

        Node initNode = new Node();
        initNode.setId(UUID.randomUUID().toString());
        initNode.setShape(CUSTOM_RECT_SHAPE);

        String dimensions = determineDimensions(featureCategoryConfig.getReuseScope());
        Content initContent = createInitContent(featureCategoryConfig, featureCategoryInfo, dimensions);
        Data initData = Data.builder()
                .componentName(INIT_COMPONENT)
                .content(initContent)
                .build();

        Attributes initAttr = createInitAttributes(featureCategoryConfig);

        initNode.setData(initData);
        initNode.setAttrs(initAttr);

        return initNode;
    }

    /**
     * 确定维度
     */
    private String determineDimensions(String reuseScope) {
        if (REUSE_SCOPE_CUST.equalsIgnoreCase(reuseScope)) {
            return DIMENSION_CUST;
        }
        if (REUSE_SCOPE_USER.equalsIgnoreCase(reuseScope)) {
            return DIMENSION_USER;
        }
        return DIMENSION_BIZ;
    }

    /**
     * 创建初始化内容
     */
    private Content createInitContent(FeatureCategoryConfigEntity featureCategoryConfig,
            FeatureCategoryInfoEntity featureCategoryInfo, String dimensions) {
        return Content.builder()
                .name(featureCategoryInfo.getFeatureCategoryName())
                .featureCategoryKey(featureCategoryConfig.getFeatureCategoryKey())
                .featureCategoryName(featureCategoryInfo.getFeatureCategoryName())
                .featureCategoryType(featureCategoryConfig.getFeatureCategoryType())
                .processDesc(featureCategoryInfo.getProcessDesc() + featureCategoryInfo.getTags())
                .param(REQUEST_PARAM)
                .status(ENABLE_STATUS)
                .dimensions(dimensions)
                .build();
    }

    /**
     * 创建初始化属性
     */
    private Attributes createInitAttributes(FeatureCategoryConfigEntity featureCategoryConfig) {
        return Attributes.builder()
                .label(Label.builder().text(featureCategoryConfig.getFeatureCategoryKey()).build())
                .image(Image.builder().xlinkHref(INIT_ICON_PATH).build())
                .text(Text.builder().text(INIT_COMPONENT_TEXT).build())
                .build();
    }

    /**
     * 创建边的工厂方法
     */
    private Edge createEdge(String sourceId, String targetId) {
        return Edge.builder()
                .id(UUID.randomUUID().toString())
                .shape(CUSTOM_EDGE_SHAPE)
                .inherit(EDGE_INHERIT)
                .source(sourceId)
                .target(targetId)
                .build();
    }
}

