package com.qihoo.feature.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qihoo.feature.dto.*;
import com.qihoo.feature.entity.fep.FeatureCategoryConfigEntity;
import com.qihoo.feature.entity.fep.FeatureCategoryInfoEntity;
import com.qihoo.feature.entity.fep.FeatureInfoEntity;
import com.qihoo.feature.entity.fep.FeatureProcessEntity;
import com.qihoo.feature.repository.fep.FeatureCategoryConfigRepository;
import com.qihoo.feature.repository.fep.FeatureCategoryInfoRepository;
import com.qihoo.feature.repository.fep.FeatureInfoRepository;
import com.qihoo.feature.repository.fep.FeatureProcessRepository;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class X6DataParser {


    @Resource
    private FeatureProcessRepository featureProcessRepository;
    @Resource
    private FeatureInfoRepository featureInfoRepository;
    @Resource
    private FeatureCategoryConfigRepository featureCategoryConfigRepository;
    @Resource
    private FeatureCategoryInfoRepository featureCategoryInfoRepository;


    public X6ComponentData convert2X6ComponentData(String featureCategoryKey) {


        Map<String /**runOrder **/, List<Node> /**node**/> levels = new TreeMap<>();
        Map<String /**namespace**/, Pair<String /**node Id **/, String /**runOrder **/>> namespaceNodeIdMap = new HashMap<>();

        Map<String/**runOrder **/, List<String> /**namespaces**/> layerDataNameMap = new HashMap<>();

        List<Edge> edges = new ArrayList<>();

        FeatureCategoryConfigEntity featureCategoryConfig = featureCategoryConfigRepository.findByFeatureCategoryKey(featureCategoryKey);

        FeatureCategoryInfoEntity featureCategoryInfo = featureCategoryInfoRepository.findByFeatureCategoryKey(featureCategoryKey);

        Node initNode = generateInitNode(featureCategoryConfig, featureCategoryInfo);
        levels.put("-9999", Lists.newArrayList(initNode));

        generateFeatureProcessNodes(featureCategoryKey, levels, layerDataNameMap, namespaceNodeIdMap, initNode, edges);

        generateFeatureElementsNode(featureCategoryConfig, namespaceNodeIdMap, edges, levels);


        List<Node> x6Nodes = autoLayoutNodes(levels);

        X6ComponentData x6Data = new X6ComponentData();
        x6Data.setEdges(edges);
        x6Data.setNodes(x6Nodes);
        return x6Data;

    }

    private void generateFeatureProcessNodes(String featureCategoryKey, Map<String, List<Node>> levels, Map<String, List<String>> layerDataNameMap, Map<String, Pair<String, String>> namespaceNodeIdMap, Node initNode, List<Edge> edges) {
        String preIndex = null;
        List<FeatureProcessEntity> enabledFeatureProcess = featureProcessRepository
                .findByFeatureCategoryKeyAndStatus(featureCategoryKey, "enable");
        enabledFeatureProcess.sort(Comparator.comparing(FeatureProcessEntity::getRunOrder));

        Map<String, List<FeatureProcessEntity>> groupByRunOrderMap = enabledFeatureProcess.stream()
                .collect(Collectors.groupingBy(FeatureProcessEntity::getRunOrder));
        TreeSet<String> orderIndex = groupByRunOrderMap.keySet()
                .stream().collect(Collectors.toCollection(TreeSet::new));
        while (!orderIndex.isEmpty()) {
            String currIndex = orderIndex.pollFirst();
            List<FeatureProcessEntity> featureProcessEntities = groupByRunOrderMap.get(currIndex);
            levels.putIfAbsent(currIndex, new ArrayList<>());
            String finalPreIndex = preIndex;
            layerDataNameMap.putIfAbsent(currIndex, new ArrayList<>());
            featureProcessEntities.forEach(p -> {
                Node node = new Node();
                Content content = Content.builder().name(p.getDataName())
                        .serviceBean(p.getServiceBean())
                        .namespace(p.getDataName())
                        .onError(p.getOnError())
                        .processorType(p.getProcessorType())
                        .status(p.getStatus())
                        .componentParams(p.getCallParams())
                        .build();
                Data data = Data.builder().componentName("javaComponent")
                        .content(content)
                        .build();

                Attributes attributes = Attributes.builder()
                        .label(Label.builder().text(p.getDataName()).build())
                        .image(Image.builder().xlinkHref("/img/java.c67ece64.svg").build())
                        .text(Text.builder().text("JAVA 组件").build())
                        .build();
                node.setId(UUID.randomUUID().toString());
                node.setData(data);
                node.setAttrs(attributes);
                List<String> deps = StringUtils.isNotBlank(p.getDependencies()) ?
                        Arrays.asList(p.getDependencies().split(",")) : new ArrayList<>();
                node.setShape("custom-rect");
                namespaceNodeIdMap.put(p.getDataName(), Pair.of(node.getId(), currIndex));
                levels.get(currIndex).add(node);
                layerDataNameMap.get(currIndex).add(p.getDataName());
                if (finalPreIndex == null) {
                    Edge edge = Edge.builder().id(UUID.randomUUID().toString())
                            .shape("custom-edge")
                            .inherit("edge")
                            .source(initNode.getId())
                            .target(node.getId()).build();
                    edges.add(edge);
                } else {
                    // 拉取上一个index的namespace
                    List<String> namespaces = layerDataNameMap.get(finalPreIndex);

                    boolean hasPreLayerDeps = CollectionUtils.isEmpty(deps) ? false :
                            Sets.intersection(Sets.newHashSet(namespaces), Sets.newHashSet(deps)).size() > 0;

                    for (String depNamespace : deps) {
                        Edge edge = Edge.builder().id(UUID.randomUUID().toString())
                                .shape("custom-edge")
                                .inherit("edge")
                                .source(namespaceNodeIdMap.get(depNamespace).getFirst())
                                .target(node.getId()).build();
                        edges.add(edge);
                    }

                    if (!hasPreLayerDeps) {
                        // 没有的情况强制加一个
                        Edge edge = Edge.builder().id(UUID.randomUUID().toString())
                                .shape("custom-edge")
                                .inherit("edge")
                                .source(namespaceNodeIdMap.get(namespaces.get(0)).getFirst())
                                .target(node.getId()).build();
                        edges.add(edge);
                    }

                }


            });
            preIndex = currIndex;
        }
    }


    /**
     * 自动布局节点位置
     * @param levels
     * @return
     */
    private List<Node> autoLayoutNodes(Map<String, List<Node>> levels) {
        int xPosition = 520;
        int yPosition = 30;

        List<Node> x6Nodes = new ArrayList<>();

        // 遍历每一层并布局（从上到下排列）
        for (String level : levels.keySet()) {

            List<Node> nodesAtLevel = levels.get(level);

            List<List<Node>> partition = Lists.partition(nodesAtLevel, 6);
            for (List<Node> nodes : partition) {
                int baseXLeft = xPosition;
                int baseXRight = xPosition;
                for (int i = 0; i < nodes.size(); i++) {
                    Node n = nodes.get(i);
                    Position p;
                    // 偏移量调整
                    int step = i % 2 == 0 ? 180 : 190;
                    if (i < nodes.size() / 2) {
                        p = Position.builder().x(baseXLeft - step * (i + 1)).y(yPosition).build();
                    } else {
                        p = Position.builder().x(baseXRight + step * (i + 1 - nodes.size() / 2)).y(yPosition).build();
                    }
                    n.setPosition(p);
                    x6Nodes.add(n);
                }
                yPosition += 65;

            }
        }
        return x6Nodes;
    }


    /**
     * 特征元组件处理
     * @param featureCategoryConfig
     * @param namespaceNodeIdMap
     * @param edges
     * @param levels
     */
    private void generateFeatureElementsNode(FeatureCategoryConfigEntity featureCategoryConfig, Map<String, Pair<String, String>> namespaceNodeIdMap, List<Edge> edges, Map<String, List<Node>> levels) {
        List<FeatureInfoEntity> featureInfos = featureInfoRepository.findByFeatureGroup(featureCategoryConfig.getFeatureGroup());

        Map<String, List<FeatureInfoEntity>> srcPathMap = featureInfos.stream().collect(Collectors.groupingBy(entity -> processSrcPath(entity.getSrcPath())));

        List<Node> featueElementNodes = new ArrayList<>();
        srcPathMap.forEach((k, v) -> {
            List<FeatureItem> featureItems = new ArrayList<>();
            v.forEach(e -> {
                FeatureItem featureItem = FeatureItem.builder().featureKey(e.getFeatureKey())
                        .dataType(e.getDataType())
                        .featureDesc(e.getFeatureName())
                        .formatAction(e.getFormatAction())
                        .namespace(e.getSrcPath())
                        .srcAttr(e.getSrcAttr())
                        .valuePath("")
                        .formatParam(e.getFormatParam())
                        .onError(e.getOnError())
                        .onEmpty(e.getOnEmpty())
                        .processLogic(e.getProcessLogic())
                        .build();
                featureItems.add(featureItem);
            });
            Node node = new Node();
            Content content = Content.builder().name(k)
                    .status("enable")
                    .featureItems(featureItems)
                    .build();

            Data data = Data.builder().componentName("featureElementComponent").content(content).build();
            Attributes attributes = Attributes.builder()
                    .label(Label.builder().text("特征元 :" + k).build())
                    .image(Image.builder().xlinkHref("/img/meta.aead177f.svg").build())
                    .text(Text.builder().text("特征元 组件").build())
                    .build();
            node.setId(UUID.randomUUID().toString());
            node.setData(data);
            node.setAttrs(attributes);

            node.setShape("custom-rect");

            Pair<String, String> nodeIdRunOrderPair = namespaceNodeIdMap.get(k);

            if (Objects.nonNull(nodeIdRunOrderPair)) {
                Edge edge = Edge.builder().id(UUID.randomUUID().toString())
                        .shape("custom-edge")
                        .inherit("edge")
                        .source(nodeIdRunOrderPair.getFirst())
                        .target(node.getId()).build();
                edges.add(edge);
                // 找不到初始节点的不写入
                featueElementNodes.add(node);
            }
        });
        levels.put("999999", featueElementNodes);
    }

    private static String processSrcPath(String srcPath) {
        if (srcPath.startsWith("DATA.")) {
            srcPath = srcPath.substring("DATA.".length());
        }
        return srcPath.split("\\.")[0]; // 按 "." 分隔，取第一个元素
    }


    /**
     * 初始化组件处理
     * @param featureCategoryConfig
     * @param featureCategoryInfo
     * @return
     */

    private Node generateInitNode(FeatureCategoryConfigEntity featureCategoryConfig, FeatureCategoryInfoEntity featureCategoryInfo) {
        Node initNode = new Node();
        String reUseScope = featureCategoryConfig.getReuseScope();
        String dimensions = "biz";
        if ("cust".equalsIgnoreCase(reUseScope)) {
            dimensions = "cust";
        }
        if ("user".equalsIgnoreCase(reUseScope)) {
            dimensions = "user";
        }
        Content initContent = Content.builder().name(featureCategoryInfo.getFeatureCategoryName())
                .featureCategoryKey(featureCategoryConfig.getFeatureCategoryKey())
                .featureCategoryName(featureCategoryInfo.getFeatureCategoryName())
                .featureCategoryType(featureCategoryConfig.getFeatureCategoryType())
                .processDesc(featureCategoryInfo.getProcessDesc() + featureCategoryInfo.getTags())
                .param("request")
                .status("enable")
                .dimensions(dimensions)
                .build();
        Data initData = Data.builder().componentName("initComponent")
                .content(initContent)
                .build();

        Attributes initAttr = Attributes.builder()
                .label(Label.builder().text(featureCategoryConfig.getFeatureCategoryKey()).build())
                .image(Image.builder().xlinkHref("/img/init.a2f7187f.svg").build())
                .text(Text.builder().text("初始化 组件").build())
                .build();
        initNode.setId(UUID.randomUUID().toString());
        initNode.setData(initData);
        initNode.setAttrs(initAttr);
        initNode.setShape("custom-rect");
        return initNode;
    }


}

