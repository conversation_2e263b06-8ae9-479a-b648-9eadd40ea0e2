package com.qihoo.finance.fep.online.fcm.feature.extractor

import com.alibaba.fastjson.JSONObject
import com.qihoo.finance.fep.common.enums.OpsSys
import com.qihoo.finance.fep.common.external.user.ExtLpsFacade
import com.qihoo.finance.fep.data.domain.DataRequestDomain
import com.qihoo.finance.fep.online.fcm.feature.extractor.BaseGroovyDataExtractor
import com.qihoo.finance.msf.api.domain.Response
import com.qihoo.finance.msf.core.utils.ResponseUtil
import groovy.transform.CompileStatic
import javax.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.apache.dubbo.rpc.service.GenericService

import static com.qihoo.finance.lps.common.codeenum.ApplSSPointEnum.APPL
import static com.qihoo.finance.lps.common.codeenum.ApplSSPointEnum.DRAW

@CompileStatic
class FaceDetectSuccessDataExtractor extends BaseGroovyDataExtractor {

    private JSONObject componentParams
    @Resource
    private ExtLpsFacade extLpsFacade

    @Override
    protected Object doHandle(DataRequestDomain request, Map<String, Object> content) throws Exception {
        String requestNo = null
        String bizType = componentParams.bizType as String

        if (StringUtils.isBlank(bizType)) {
            bizType = APPL.getCode()
        }

        if (APPL.getCode().equals(bizType)) {
            requestNo = request.getLpsApplNo()
        } else if (DRAW.getCode().equals(bizType)) {
            requestNo = request.getLpsDrawNo()
        } else if (OpsSys.OCAS.getCode().equals(bizType)) {
            requestNo = request.getCasCaseNo()
        }

        if (StringUtils.isBlank(requestNo)) {
            return null
        }

        if (StringUtils.isBlank(componentParams.forceQuery as String)) {
            return extLpsFacade.queryFaceDetectSuccessList(request.getUserNo(), requestNo, bizType)
        }

        def invokeParams = ["userNo": request.getUserNo(), "relFlowNo": requestNo, "detectType": bizType]
        def params = [invokeParams]
        def paramTypes = []
        paramTypes << "com.qihoo.finance.cis.modules.user.request.FaceDetectRequest"
        return invokeDubbo("com.qihoo.finance.cis.modules.user.UserCertFacade", "queryFaceDetectSuccessList", paramTypes as String[], params as Object[])
    }

    @Override
    void setParams(JSONObject params) {
        this.componentParams = params
    }
}