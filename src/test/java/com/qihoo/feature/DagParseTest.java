package com.qihoo.feature;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.qihoo.feature.config.DateColumnMapRowMapper;
import com.qihoo.feature.dto.X6ComponentData;
import com.qihoo.feature.entity.fep.FeatureProcessEntity;
import com.qihoo.feature.repository.fep.FeatureProcessRepository;
import com.qihoo.feature.service.X6DataParser;
import jakarta.annotation.Resource;
import org.apache.tomcat.util.buf.HexUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
public class DagParseTest {

    @Resource
    private X6DataParser x6DataParser;

    @Resource
    private FeatureProcessRepository featureProcessRepository;


    @Resource
    private ObjectMapper objectMapper;


    @Resource(name = "localJdbcTemplate")
    private NamedParameterJdbcTemplate jdbcTemplate;

    @Test
    public void queryByJDBC() {
        final List<Map<String, Object>> maps = jdbcTemplate.query("select * from fcm_resource where id = :id",
                Map.of("id", 1, "name", "22"), new DateColumnMapRowMapper()
        );

        for (Map<String, Object> map : maps) {
            map.forEach((k, v) -> {
                System.out.println("key :" + k + " value :" + v + " type :" + v.getClass());
            });
        }
    }


    @Test
    public void parse2Test() throws Exception {

        String  featureCategoryKey ="jpAppDataCas";
        X6ComponentData data = x6DataParser.convert2X6ComponentData(featureCategoryKey);
        String jsonStr = objectMapper.writeValueAsString(data);

        String hexString = HexUtils.toHexString(jsonStr.getBytes());
        System.out.println(hexString);
        Files.writeString(Paths.get(featureCategoryKey +".json"), jsonStr, StandardOpenOption.CREATE_NEW);
        System.err.println(jsonStr);
    }

    @Test
    public void testArrange() throws Exception {
/*        final List<FeatureProcessEntity> featureProcessEntities = featureProcessRepository.findByFeatureCategoryKeyAndStatus("faceResult", "enable");
        TreeMap<String, List<FeatureProcessEntity>> sortedFeatureProcessMap = featureProcessEntities
                .stream()
                .collect(Collectors.groupingBy(FeatureProcessEntity::getRunOrder,
                        TreeMap::new,
                        Collectors.toList()
                ));
        for (Map.Entry<String, List<FeatureProcessEntity>> entry : sortedFeatureProcessMap.entrySet()) {
            System.err.println("k = " + entry.getKey() + "  value = " + objectMapper.writeValueAsString(entry.getValue()));
        }*/


    }

    @Test
    public void testMap() {
        List<Integer> lst = Arrays.asList(2, 1, 3, 5);
        Map<String, List<Integer>> map = new HashMap<>();
        map.put("lst", ImmutableList.copyOf(lst));
        List<Integer> l11 = map.get("lst").stream().sorted().toList();
       // Collections.sort(l11);
        System.err.println(map);

    }
}
