package com.qihoo.feature;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.SerializationUtils;

import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
class FeatureTransApplicationTests {

    @Test
    void contextLoads() throws Exception {

        RandomAccessFile file = new RandomAccessFile("example.txt", "rw");
        FileChannel channel = file.getChannel();
        FileLock lock = channel.lock();  // 锁定文件

        try {
            // 写入操作
            file.setLength(0);
            file.write("11".getBytes());
        } finally {
            lock.release();  // 释放锁
            file.close();  // 关闭文件
        }

    }

    @Resource(name = "localJdbcTemplate")
    private JdbcTemplate localJdbcTemplate;

    @Test
    public void getTableSchema() {
        // 使用 JdbcTemplate 获取数据库连接
        localJdbcTemplate.execute((Connection conn) -> {
            // 获取 DatabaseMetaData 对象
            DatabaseMetaData metaData = conn.getMetaData();

            // 获取数据库中的所有 schema
            ResultSet schemas = metaData.getCatalogs();
            while (schemas.next()) {
                String schemaName = schemas.getString("TABLE_CAT");
              //  System.out.println("Schema: " + schemaName);

                // 获取 schema 中的所有表
                ResultSet tables = metaData.getTables(null, schemaName, null, new String[]{"TABLE"});
                while (tables.next()) {
                    String tableName = tables.getString("TABLE_NAME");
                    //System.out.println("  Table: " + tableName);
                    // 获取表的列信息
                    ResultSet columns = metaData.getColumns(null, schemaName, tableName, null);
                    while (columns.next()) {
                        String columnName = columns.getString("COLUMN_NAME");
                        String columnType = columns.getString("TYPE_NAME");
                        int columnSize = columns.getInt("COLUMN_SIZE");
                        System.out.println("database : " + schemaName + " + table : " + tableName + "    Column: " + columnName + ", Type: " + columnType + ", Size: " + columnSize);
                    }
                    columns.close();
                }
                tables.close();
            }
            schemas.close();
            return null;
        });
    }
}
