import com.alibaba.fastjson2.JSON

String2JSONArrayDataProcessor


String dep = (String) componentParams?.depName
List<String> convertKeys = (List<String>) componentParams?.convertKeys

Map<String, Object> data4Process = (Map<String, Object>) dependencies.get(dep)

def res = [:]
if (convertKeys && data4Process) {
    convertKeys.forEach { it ->
        if (data4Process.get(it) instanceof String) {
            res.put(it, JSON.parseArray((String) data4Process.get(it)))
        }

    }
}
return res
