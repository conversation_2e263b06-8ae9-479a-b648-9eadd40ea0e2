package com.qihoo.feature;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public class test2 {

    @Test
    public void str() {
        String showMockUsers = "zhujie,songrongxin";
        String user = "zhujie";

        boolean showMock = Arrays.stream(Optional.ofNullable(showMockUsers)
                        .orElse("").split(","))
                .filter(
                        it -> "ALL".equalsIgnoreCase(it) || Objects.equals(it, user))
                .findFirst().isPresent();
        System.out.println(showMock);
        String date ="2025-06-27 00:00:00";
        System.err.println(date.substring(0,10));
        System.err.println(date.substring(0,10).length());
    }
}
