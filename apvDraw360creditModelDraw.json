{"nodes": [{"id": "e935d5a4-a471-4b2d-8f40-e298be3835f8", "shape": "custom-rect", "data": {"componentName": "initComponent", "content": {"name": "apvDraw360creditModelDraw模型结果", "featureCategoryKey": "apvDraw360creditModelDraw", "featureCategoryName": "apvDraw360creditModelDraw模型结果", "processDesc": "从dcd查询apvDraw360creditModelDraw模型结果不限产品,实时计算,模型结果,模型结果", "featureCategoryType": "JSONOBJECT", "dimensions": "biz", "param": "request", "status": "enable"}}, "attrs": {"text": {"text": "初始化 组件"}, "image": {"xlink:href": "/img/init.a2f7187f.svg"}, "label": {"text": "apvDraw360creditModelDraw"}}, "position": {"x": 700.0, "y": 30.0}}, {"id": "4736fc0b-28b4-48c1-a30f-24c65c629d63", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "draw", "status": "enable", "namespace": "draw", "componentParams": "{}", "onError": "T", "serviceBean": "drawExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "draw"}}, "position": {"x": 700.0, "y": 95.0}}, {"id": "7808d893-3536-4340-924a-aec08043e893", "shape": "custom-rect", "data": {"componentName": "javaComponent", "content": {"name": "creditModelDrawModelResult", "status": "enable", "namespace": "creditModelDrawModelResult", "componentParams": "{\"bizType\":\"DRAW\",\"modelName\":\"360credit-model-draw\",\"moduleNo\":\"APV\",\"exchangeModelName\":\"Y\"}", "onError": "T", "serviceBean": "eepModelResultMapDataExtractor", "processorType": "EXTRACTOR"}}, "attrs": {"text": {"text": "JAVA 组件"}, "image": {"xlink:href": "/img/java.c67ece64.svg"}, "label": {"text": "creditModelDrawModelResult"}}, "position": {"x": 700.0, "y": 160.0}}, {"id": "2897e765-c34a-4b2c-848d-d97435796905", "shape": "custom-rect", "data": {"componentName": "featureElementComponent", "content": {"name": "creditModelDrawModelResult", "status": "enable", "featureItems": [{"featureKey": "assetTypeChange", "featureDesc": "", "namespace": "creditModelDrawModelResult", "valuePath": "", "srcAttr": "assetTypeChange", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": "apvDraw360creditModelDraw模型结果"}, {"featureKey": "tradeType", "featureDesc": "", "namespace": "creditModelDrawModelResult", "valuePath": "", "srcAttr": "tradeType", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": "apvDraw360creditModelDraw模型结果"}, {"featureKey": "tradeScore", "featureDesc": "", "namespace": "creditModelDrawModelResult", "valuePath": "", "srcAttr": "tradeScore", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": "apvDraw360creditModelDraw模型结果"}, {"featureKey": "jt_c_api_trade3_v5_230731_val", "featureDesc": "1.0", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "jt_c_api_trade3_v5_230731_val", "formatAction": "", "formatParam": "", "dataType": "Double", "onError": "R_E", "onEmpty": "R", "processLogic": ""}, {"featureKey": "result", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "result", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "rejectCode", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "rejectCode", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "custRiskLevel", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "custRiskLevel", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "assetTypeChange", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "assetTypeChange", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "tradeType", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "tradeType", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "tradeScore", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "tradeScore", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "assetTypeChangeBianji", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "assetTypeChangeBianji", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "apiTrade1Mob3V20210427", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "apiTrade1Mob3V20210427", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "apiTrade2Mob3V20210617", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "apiTrade2Mob3V20210617", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "apiTrade2EnseV20210617", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "apiTrade2EnseV20210617", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "apiTrade3Mob3V20210629", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "apiTrade3Mob3V20210629", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "apiTrade3Mob3FeiMtV20210629", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "apiTrade3Mob3FeiMtV20210629", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "jsType", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "jsType", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "jsResult", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "jsResult", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "jsReason", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "jsReason", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "jsDays", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "jsDays", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "jsCode", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "jsCode", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "baiWeiScore", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "baiWeiScore", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ice36Result", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ice36Result", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "lianyang_qilinc", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "lianyang_qilinc", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "bytedance_score", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "bytedance_score", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ice36P0Fail", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ice36P0Fail", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "umeng_s3831", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "umeng_s3831", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "bytedance17386_score", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "bytedance17386_score", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "anti_fraud11086_score", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "anti_fraud11086_score", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "apifraud_mixscore", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "apifraud_mixscore", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ice36DoudiType", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ice36DoudiType", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "hengpu_k23", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "hengpu_k23", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "baiyun_score_cust7", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "baiyun_score_cust7", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ant_duanka", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ant_duanka", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "dongcha_scorea1", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "dongcha_scorea1", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "last_cust_seg", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "last_cust_seg", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "cust_seg_adj", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "cust_seg_adj", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "loan_amt_adj", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "loan_amt_adj", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "is_adjust_tag", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "is_adjust_tag", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "adjust_date", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "adjust_date", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "adjust_type", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "adjust_type", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ice36TransferCode", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ice36TransferCode", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ice36P0FailType", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ice36P0FailType", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ice24DoudiType", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ice24DoudiType", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "temp_price_exp_date", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "temp_price_exp_date", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "credit_amt", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "credit_amt", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "temp_add_credit_amt", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "temp_add_credit_amt", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "temp_exp_date", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "temp_exp_date", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "adjust_backtype", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "adjust_backtype", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ice36LoanType", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ice36LoanType", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "ExclFunderList", "featureDesc": "智信辅助路由分发3.0不路由资方", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "ExclFunderList", "formatAction": "", "formatParam": "", "dataType": "String", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}, {"featureKey": "bonus_amt", "featureDesc": "", "namespace": "DATA.creditModelDrawModelResult", "valuePath": "", "srcAttr": "bonus_amt", "formatAction": "", "formatParam": "", "dataType": "", "onError": "R_E", "onEmpty": "R_W", "processLogic": ""}]}}, "attrs": {"text": {"text": "特征元 组件"}, "image": {"xlink:href": "/img/meta.aead177f.svg"}, "label": {"text": "特征元 :creditModelDrawModelResult"}}, "position": {"x": 700.0, "y": 225.0}}], "edges": [{"id": "20e1eb10-1b77-4c63-aece-4e3dcf3735b4", "shape": "custom-edge", "source": "e935d5a4-a471-4b2d-8f40-e298be3835f8", "target": "4736fc0b-28b4-48c1-a30f-24c65c629d63", "inherit": "edge"}, {"id": "62f2b5ec-b651-41f6-88b7-79491a4d3b3b", "shape": "custom-edge", "source": "4736fc0b-28b4-48c1-a30f-24c65c629d63", "target": "7808d893-3536-4340-924a-aec08043e893", "inherit": "edge"}, {"id": "9e09333b-5980-45f4-9157-4a8dde5c7b8d", "shape": "custom-edge", "source": "7808d893-3536-4340-924a-aec08043e893", "target": "2897e765-c34a-4b2c-848d-d97435796905", "inherit": "edge"}]}